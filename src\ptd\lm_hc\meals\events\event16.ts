import { Player, system } from '@minecraft/server';

/**
 * Event 16: Teleport to the End
 * Places an end portal block at the player's location and
 * teleports them to The End dimension.
 *
 * Portal Logic:
 * 1. Places an end portal block at player's location
 * 2. Teleports player to The End dimension near spawn point
 *
 * @param player - The player object who triggered the event
 * @returns {void} - Function exits after successful teleportation
 */
export async function event16(player: Player): Promise<void> {
  try {
    // Get player's current location and dimension
    const playerLocation = player.location;

    // Place end portal block at player location
    const block = player.dimension.getBlock(playerLocation);
    if (!block) {
      throw new Error('Could not get block at player location');
    }

    block.setType('minecraft:end_portal');

    // Remove the end portal after 1 tick
    await system.waitTicks(1);
    block.setType('minecraft:air');
    console.warn('End portal block removed after teleportation.');
  } catch (error) {
    console.warn(`Failed to execute event 16 teleport: ${error}`);
  }
  return;
}
