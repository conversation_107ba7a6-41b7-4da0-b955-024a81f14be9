{"tiers": [{"total_exp_required": 0, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:wheat", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 2}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}, {"wants": [{"item": "minecraft:potato", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 2}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}, {"wants": [{"item": "minecraft:beetroot", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 2}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}, {"wants": [{"item": "minecraft:carrot", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 2}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:bread", "quantity": 32}], "trader_exp": 1, "max_uses": 16, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:cookie", "quantity": 32}], "trader_exp": 1, "max_uses": 16, "reward_exp": true}]}]}, {"total_exp_required": 10, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:pumpkin", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 3}], "trader_exp": 10, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:apple", "quantity": 32}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:pumpkin_pie", "quantity": 16}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 70, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:melon_block", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 3}], "trader_exp": 20, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:cake", "quantity": 5}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:golden_carrot", "quantity": 16}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 150, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:beetroot_seeds", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 2}], "trader_exp": 20, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:golden_apple", "quantity": 5}], "trader_exp": 15, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:enchanted_golden_apple", "quantity": 1}], "trader_exp": 15, "max_uses": 3, "reward_exp": true}]}]}]}