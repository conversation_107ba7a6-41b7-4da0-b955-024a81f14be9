﻿{
  "format_version": "1.20.50",
  "minecraft:attachable": {
    "description": {
      "identifier": "ptd_lmhc:bonbon_bowl",
      "materials": {
        "default": "entity_alphatest"
      },
      "textures": {
        "default": "textures/ptd/lm_hc/items/food/bonbon_bowl",
        "enchanted": "textures/misc/enchanted_item_glint"
      },
      "geometry": {
        "default": "geometry.bonbon_bowl"
      },
      "animations": {
        "hold_first_person": "animation.food.bonbon_bowl.hold_first_person",
        "hold_third_person": "animation.food.bonbon_bowl.hold_third_person",
        "eat": "animation.food.bonbon_bowl.eat",
        "eat_controller": "controller.animation.food.eat",
        "general": "controller.animation.food.general"
      },
      "scripts": {
        "animate": ["general", "eat_controller"]
      },
      "render_controllers": ["controller.render.food"]
    }
  }
}


