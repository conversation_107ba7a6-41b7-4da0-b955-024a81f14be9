{"pools": [{"rolls": {"min": 5, "max": 5}, "entries": [{"type": "item", "name": "minecraft:diamond", "weight": 10, "functions": [{"function": "set_count", "count": {"min": 1, "max": 5}}]}, {"type": "item", "name": "minecraft:netherite_ingot", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:gold_block", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "minecraft:iron_block", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}]}, {"type": "item", "name": "minecraft:emerald", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 1, "max": 5}}]}, {"type": "item", "name": "minecraft:enchanted_golden_apple", "weight": 5, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "minecraft:golden_apple", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 1, "max": 5}}]}, {"type": "item", "name": "minecraft:netherite_sword", "weight": 6, "functions": [{"function": "enchant_randomly"}]}, {"type": "item", "name": "minecraft:netherite_helmet", "weight": 6, "functions": [{"function": "enchant_randomly"}]}, {"type": "item", "name": "minecraft:netherite_chestplate", "weight": 6, "functions": [{"function": "enchant_randomly"}]}, {"type": "item", "name": "minecraft:netherite_leggings", "weight": 6, "functions": [{"function": "enchant_randomly"}]}, {"type": "item", "name": "minecraft:netherite_boots", "weight": 6, "functions": [{"function": "enchant_randomly"}]}, {"type": "item", "name": "minecraft:trident", "weight": 4, "functions": [{"function": "enchant_with_levels", "levels": {"min": 50, "max": 250}}]}, {"type": "item", "name": "minecraft:elytra", "weight": 4, "functions": [{"function": "enchant_with_levels", "levels": {"min": 50, "max": 100}}]}, {"type": "item", "name": "minecraft:totem_of_undying", "weight": 5, "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}]}, {"type": "item", "name": "minecraft:beacon", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "minecraft:end_crystal", "weight": 4, "functions": [{"function": "set_count", "count": {"min": 1, "max": 1}}]}, {"type": "item", "name": "minecraft:experience_bottle", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 1, "max": 5}}]}, {"type": "item", "name": "minecraft:netherite_pickaxe", "weight": 6, "functions": [{"function": "enchant_randomly"}]}, {"type": "item", "name": "minecraft:netherite_axe", "weight": 5, "functions": [{"function": "enchant_randomly"}]}, {"type": "item", "name": "minecraft:netherite_shovel", "weight": 5, "functions": [{"function": "enchant_randomly"}]}, {"type": "item", "name": "minecraft:netherite_hoe", "weight": 5, "functions": [{"function": "enchant_randomly"}]}, {"type": "item", "name": "minecraft:nether_star", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:conduit", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "minecraft:heart_of_the_sea", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 1, "max": 1}}]}, {"type": "item", "name": "minecraft:shulker_shell", "weight": 6, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:echo_shard", "weight": 5, "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}]}, {"type": "item", "name": "minecraft:wither_skeleton_skull", "weight": 4, "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}]}, {"type": "item", "name": "minecraft:ancient_debris", "weight": 6, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "minecraft:netherite_block", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 1, "max": 1}}]}, {"type": "item", "name": "minecraft:emerald_block", "weight": 5, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "minecraft:diamond_block", "weight": 5, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "minecraft:budding_amethyst", "weight": 4, "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}]}, {"type": "item", "name": "minecraft:turtle_egg", "weight": 2, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:sponge", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}]}, {"type": "item", "name": "minecraft:lodestone", "weight": 4, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:crying_obsidian", "weight": 5, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:sculk_catalyst", "weight": 2, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "minecraft:music_disc_5", "weight": 1}, {"type": "item", "name": "minecraft:music_disc_otherside", "weight": 2}, {"type": "item", "name": "minecraft:music_disc_pigstep", "weight": 2}, {"type": "item", "name": "minecraft:recovery_compass", "weight": 3}, {"type": "item", "name": "minecraft:netherite_upgrade_smithing_template", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}]}]}]}