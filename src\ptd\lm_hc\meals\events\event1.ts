import { Player, Vector3, ItemStack } from '@minecraft/server';
import { onEatMeal } from '../events';
import { chestFiller } from '../../utilities/chestFiller';
import { getRandomInt } from '../../utilities/rng';

/**
 * Event 1: Treasure Trove
 *
 * Attempts to spawn a chest containing valuable resources
 * in front of the player using their exact view direction vector.
 *
 * Spawning Logic:
 * 1. Uses player's view direction to calculate spawn location
 * 2. Rounds the direction vector to get the closest cardinal direction
 * 3. If blocked, tries alternate positions in clockwise order
 * 4. Spawns a firework rocket for visual feedback when chest appears
 * 5. Fills chest with ingots and valuable items in random slots
 *
 * Contents:
 * - Valuable resources (ingots and gems)
 *
 * @param {Player} player - The player who consumed the lucky meal
 * @returns {void} - Function exits after successful chest spawn or retry attempt
 */
export function event1(player: Player): void {
  try {
    // Get player's view direction and round to nearest cardinal direction
    const viewDir = player.getViewDirection();
    const preferredDirection: Vector3 = {
      x: Math.abs(viewDir.x) > Math.abs(viewDir.z) ? Math.sign(viewDir.x) : 0,
      y: 0,
      z: Math.abs(viewDir.z) > Math.abs(viewDir.x) ? Math.sign(viewDir.z) : 0
    };

    // Calculate alternate directions clockwise from preferred direction
    const directions: Vector3[] = [
      preferredDirection,
      { x: -preferredDirection.z, y: 0, z: preferredDirection.x },
      { x: -preferredDirection.x, y: 0, z: -preferredDirection.z },
      { x: preferredDirection.z, y: 0, z: -preferredDirection.x }
    ];

    // Try each direction starting with the preferred one
    for (const direction of directions) {
      const spawnLocation = {
        x: Math.floor(player.location.x + direction.x * 2),
        y: Math.floor(player.location.y),
        z: Math.floor(player.location.z + direction.z * 2)
      };

      const block = player.dimension.getBlock(spawnLocation);
      if (block?.isAir || block?.isLiquid) {
        block.setType('minecraft:chest');
        const chest = player.dimension.getBlock(spawnLocation);
        if (chest) {
          chest.dimension.spawnEntity('minecraft:fireworks_rocket', spawnLocation);
          chest.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnLocation);
          chest.dimension.playSound('mob.endermen.portal', spawnLocation);

          // Create an array of valuable items to put in the chest
          const items: ItemStack[] = [];

          // Define possible item types with their min/max counts
          interface ItemOption {
            id: string;
            minCount: number;
            maxCount: number;
            weight: number;
            stacks?: number;
          }

          // Add 1-3 slots of diamonds, with 2-3 diamonds per slot
          const diamondSlots = getRandomInt(1, 3);
          for (let i = 0; i < diamondSlots; i++) {
            const diamondsPerSlot = getRandomInt(2, 3);
            items.push(new ItemStack('minecraft:diamond', diamondsPerSlot));
          }

          const possibleItems: ItemOption[] = [
            // Diamonds are now handled separately above
            { id: 'minecraft:iron_ingot', minCount: 3, maxCount: 10, weight: 10 },
            { id: 'minecraft:gold_ingot', minCount: 5, maxCount: 12, weight: 10 },
            { id: 'minecraft:emerald', minCount: 1, maxCount: 3, weight: 8 },
            { id: 'minecraft:redstone', minCount: 15, maxCount: 30, weight: 12 },
            { id: 'minecraft:lapis_lazuli', minCount: 5, maxCount: 9, weight: 10 },
            { id: 'minecraft:netherite_ingot', minCount: 1, maxCount: 1, weight: 5 }
          ];

          // Calculate total weight for weighted random selection
          const totalWeight = possibleItems.reduce((sum, item) => sum + item.weight, 0);

          // Function to select a weighted random item
          const selectWeightedRandomItem = (): ItemOption => {
            // Ensure we have items to choose from - this check allows TypeScript to know
            // that possibleItems[0] won't be undefined in the return statement
            if (possibleItems.length === 0) {
              // Default fallback if no items are available
              return { id: 'minecraft:diamond', minCount: 1, maxCount: 64, weight: 1 };
            }

            let randomWeight = getRandomInt(1, totalWeight);

            for (const item of possibleItems) {
              randomWeight -= item.weight;
              if (randomWeight <= 0) {
                return item;
              }
            }

            // Fallback to first item if something goes wrong with the random selection
            // Using non-null assertion since we already checked possibleItems.length above
            return possibleItems[0]!;
          };

          // Generate random items for the remaining chest slots
          const remainingSlots = 9 - diamondSlots;
          for (let i = 0; i < remainingSlots; i++) {
            // Get a random item based on weight
            const selectedItem = selectWeightedRandomItem();

            // Generate a random count within the min/max range
            const count = getRandomInt(selectedItem.minCount, selectedItem.maxCount);

            // For items with multiple stacks
            if (selectedItem.stacks && selectedItem.stacks > 1) {
              // Create stacks of the item (up to MAX_STACK_SIZE of 64)
              const MAX_STACK_SIZE = 64;
              const totalItems = count * selectedItem.stacks;

              // Calculate how many full stacks we need
              const fullStacks = Math.floor(totalItems / MAX_STACK_SIZE);
              // Calculate remaining items that don't fill a complete stack
              const remainder = totalItems % MAX_STACK_SIZE;

              // Add full stacks
              for (let j = 0; j < fullStacks; j++) {
                items.push(new ItemStack(selectedItem.id, MAX_STACK_SIZE));
              }

              // Add remaining partial stack if any
              if (remainder > 0) {
                items.push(new ItemStack(selectedItem.id, remainder));
              }
            } else {
              // For single stack items, just create one stack with the count
              items.push(new ItemStack(selectedItem.id, Math.min(count, 64)));
            }
          }

          // Use chestFiller to place items in random slots
          chestFiller(chest, items, true, false);

          return;
        }
      }
    }

    console.warn('No suitable location found for spawning chest.');
    onEatMeal(player); // Retry
  } catch (error) {
    console.warn(`Failed to spawn treasure chest: ${error}`);
    onEatMeal(player); // Retry
  }
  return;
}
