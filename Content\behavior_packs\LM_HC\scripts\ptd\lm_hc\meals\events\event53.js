import { ItemStack } from '@minecraft/server';
/**
 * Event 53: Beacon of Hope
 * Gives the player a beacon
 * @param player The player who triggered the event
 */
export function event53(player) {
    try {
        // Create a beacon item
        const beacon = new ItemStack('minecraft:beacon', 1);
        // Add the beacon to player's inventory
        const inventory = player.getComponent('inventory');
        if (inventory) {
            inventory.container?.addItem(beacon);
        }
    }
    catch (error) {
        console.error(`Error in event53: ${error}`);
    }
    return;
}
