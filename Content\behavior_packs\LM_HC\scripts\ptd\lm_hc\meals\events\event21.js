import { system } from '@minecraft/server';
/**
 * Event 21: Super Speed Boots
 * Spawns netherite boots with soul speed 3, feather falling 4, depth strider 5, unbreaking 3, thorns 3, and mending enchantments
 * @param player The player who triggered the event
 */
export async function event21(player) {
    try {
        const dimension = player.dimension;
        const spawnLocation = player.location;
        // Wait for 1 tick to ensure everything is properly initialized
        await system.waitTicks(1);
        // Spawn the enchanted netherite boots using loot table
        dimension.runCommand(`loot spawn ${spawnLocation.x} ${spawnLocation.y} ${spawnLocation.z} loot "ptd/lm_hc/event21"`);
        // Add visual and sound effects
        dimension.playSound('random.levelup', spawnLocation, { volume: 0.8 });
        for (let i = 0; i < 15; i++) {
            dimension.spawnParticle('minecraft:totem_particle', spawnLocation);
        }
    }
    catch (error) {
        console.warn(`Failed to execute event 21 (Super Speed Boots): ${error}`);
    }
    return;
}
