{"tiers": [{"trades": [{"wants": [{"item": "minecraft:coal:0", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 5}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:diamond_helmet", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:iron_ingot", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 5}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:diamond_chestplate", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:shield", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:diamond_leggings", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:diamond", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 16}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:diamond_boots", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:bell"}], "gives": [{"item": "minecraft:emerald", "quantity": 32}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:netherite_armor", "quantity": 4}]}]}]}