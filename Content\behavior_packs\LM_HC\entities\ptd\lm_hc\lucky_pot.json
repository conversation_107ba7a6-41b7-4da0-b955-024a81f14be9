{"format_version": "1.21.70", "minecraft:entity": {"description": {"identifier": "ptd_lmhc:lucky_pot", "is_spawnable": true, "is_summonable": true, "is_experimental": false, "properties": {"ptd_lmhc:give_food": {"client_sync": true, "type": "bool", "default": false}, "ptd_lmhc:ingredient_count": {"client_sync": true, "type": "int", "default": 0, "range": [0, 5]}, "ptd_lmhc:times_to_destroy": {"client_sync": true, "type": "int", "default": 0, "range": [0, 7]}}, "animations": {"idle_sounds": "animation.lucky_pot.idle_sounds"}, "scripts": {"animate": ["idle_sounds"]}}, "events": {"ptd_lmhc:add_ingredient": {"set_property": {"ptd_lmhc:ingredient_count": "q.property('ptd_lmhc:ingredient_count') + 1"}}, "ptd_lmhc:give_food": {"sequence": [{"add": {"component_groups": ["ptd_lmhc:giving_food"]}}, {"set_property": {"ptd_lmhc:give_food": true}}]}, "ptd_lmhc:on_give_food": {"sequence": [{"remove": {"component_groups": ["ptd_lmhc:giving_food"]}}, {"set_property": {"ptd_lmhc:give_food": false, "ptd_lmhc:ingredient_count": 0}}]}, "ptd_lmhc:increment_destroy_times": {"sequence": [{"add": {"component_groups": ["ptd_lmhc:reset_destroy_times"]}}, {"set_property": {"ptd_lmhc:times_to_destroy": "math.min(q.property('ptd_lmhc:times_to_destroy') + 1, 7)"}}]}, "ptd_lmhc:reset_destroy_times": {"sequence": [{"remove": {"component_groups": ["ptd_lmhc:giving_food"]}}, {"set_property": {"ptd_lmhc:times_to_destroy": 0}}]}}, "component_groups": {"ptd_lmhc:reset_destroy_times": {"minecraft:timer": {"time": 0.8, "time_down_event": {"event": "ptd_lmhc:reset_destroy_times"}}}, "ptd_lmhc:giving_food": {"minecraft:timer": {"time": 0.8, "time_down_event": {"event": "ptd_lmhc:on_give_food"}}}}, "components": {"minecraft:health": {"value": 10, "max": 10}, "minecraft:collision_box": {"width": 1.1, "height": 1.2}, "minecraft:is_collidable": {}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no", "on_damage": {"event": "ptd_lmhc:increment_destroy_times"}}]}, "minecraft:knockback_resistance": {"value": 1.0}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_sneaking", "subject": "other", "value": false}, {"test": "bool_property", "subject": "self", "domain": "ptd_lmhc:give_food", "value": false}, {"test": "int_property", "subject": "self", "domain": "ptd_lmhc:ingredient_count", "operator": "<", "value": 5}]}}, "swing": true, "interact_text": "action.interact.lucky_pot.add_ingredient"}]}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}}}}