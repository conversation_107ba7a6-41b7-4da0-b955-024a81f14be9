﻿{
  "format_version": "1.20.50",
  "minecraft:attachable": {
    "description": {
      "identifier": "ptd_lmhc:blueberry_waffle",
      "materials": {
        "default": "entity_alphatest"
      },
      "textures": {
        "default": "textures/ptd/lm_hc/items/food/blueberry_waffle",
        "enchanted": "textures/misc/enchanted_item_glint"
      },
      "geometry": {
        "default": "geometry.blueberry_waffle"
      },
      "animations": {
        "hold_first_person": "animation.food.blueberry_waffle.hold_first_person",
        "hold_third_person": "animation.food.blueberry_waffle.hold_third_person",
        "eat": "animation.food.blueberry_waffle.eat",
        "eat_controller": "controller.animation.food.eat",
        "general": "controller.animation.food.general"
      },
      "scripts": {
        "animate": ["general", "eat_controller"]
      },
      "render_controllers": ["controller.render.food"]
    }
  }
}


