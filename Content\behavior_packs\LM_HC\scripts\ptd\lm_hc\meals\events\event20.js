import { EffectTypes } from '@minecraft/server';
/**
 * Event 20: Instantly drops the player's hunger bar to zero.
 * Instantly drops the player's hunger bar to zero by applying a hunger effect.
 * @param player The player who triggered the event.
 */
export function event20(player) {
    try {
        // Apply hunger effect with level 255 for instant hunger depletion
        player.addEffect(EffectTypes.get('hunger'), 200, { amplifier: 255, showParticles: false });
    }
    catch (error) {
        console.error(`Failed to execute event 20: ${error}`);
    }
    return;
}
