import { Player, ExplosionOptions, Vector3 } from '@minecraft/server';

/**
 * Event 50: Detonation
 * Creates an explosion 2 blocks below the player's location
 * @param player The player who triggered the event
 */
export function event50(player: Player): void {
  try {
    // Create explosion options
    const explosionOptions: ExplosionOptions = {
      breaksBlocks: true,
      causesFire: false,
      allowUnderwater: true
    };

    // Create explosion location 1 block below the player
    const explosionLocation: Vector3 = {
      x: player.location.x,
      y: player.location.y - 1,
      z: player.location.z
    };

    // Create a single explosion at the offset location
    player.dimension.createExplosion(explosionLocation, 12.0, explosionOptions);
  } catch (error) {
    console.warn(`Failed to execute event 50: ${error}`);
  }
  return;
}
