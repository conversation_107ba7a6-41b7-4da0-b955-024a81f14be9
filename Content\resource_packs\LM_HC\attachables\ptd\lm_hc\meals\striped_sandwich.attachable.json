﻿{
  "format_version": "1.20.50",
  "minecraft:attachable": {
    "description": {
      "identifier": "ptd_lmhc:striped_sandwich",
      "materials": {
        "default": "entity_alphatest"
      },
      "textures": {
        "default": "textures/ptd/lm_hc/items/food/striped_sandwich",
        "enchanted": "textures/misc/enchanted_item_glint"
      },
      "geometry": {
        "default": "geometry.striped_sandwich"
      },
      "animations": {
        "hold_first_person": "animation.food.striped_sandwich.hold_first_person",
        "hold_third_person": "animation.food.striped_sandwich.hold_third_person",
        "eat": "animation.food.striped_sandwich.eat",
        "eat_controller": "controller.animation.food.eat",
        "general": "controller.animation.food.general"
      },
      "scripts": {
        "animate": ["general", "eat_controller"]
      },
      "render_controllers": ["controller.render.food"]
    }
  }
}


