{"tiers": [{"trades": [{"wants": [{"item": "minecraft:string", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 4}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:arrow", "quantity": 64}]}]}, {"trades": [{"wants": [{"item": "minecraft:gravel", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 2}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:bow", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:crossbow", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}, {"wants": [{"item": "minecraft:feather", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 2}]}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:tipped_arrow", "quantity": 16, "functions": [{"function": "set_data", "data": 6}]}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:tipped_arrow", "quantity": 16, "functions": [{"function": "set_data", "data": 25}]}]}]}]}