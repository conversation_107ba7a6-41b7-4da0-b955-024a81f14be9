{"format_version": "1.20.41", "minecraft:entity": {"description": {"identifier": "ptd_lmhc:angry_bee", "is_spawnable": false, "is_summonable": true, "is_experimental": false, "properties": {"minecraft:has_nectar": {"type": "bool", "client_sync": true, "default": "query.had_component_group('has_nectar')"}, "ptd_lmhc:already_stung": {"type": "bool", "client_sync": true, "default": false}}}, "component_groups": {"ptd_lmhc:angry": {"minecraft:angry": {"duration": 9999, "broadcast_anger": true, "broadcast_targets": ["bee_dragon_bee"], "broadcast_range": 20, "calm_event": {"event": "ptd_lmhc:calmed_down", "target": "self"}}}, "ptd_lmhc:start_die_timer": {"minecraft:timer": {"time": 5, "looping": false, "time_down_event": {"event": "ptd_lmhc:die", "target": "self"}}}, "ptd_lmhc:dead": {"minecraft:damage_over_time": {"damage_per_hurt": 99999, "time_between_hurt": 0}}}, "events": {"ptd_lmhc:angry": {"add": {"component_groups": ["ptd_lmhc:angry"]}}, "ptd_lmhc:calmed_down": {"remove": {"component_groups": ["ptd_lmhc:angry"]}}, "ptd_lmhc:start_die_timer": {"sequence": [{"set_property": {"ptd_lmhc:already_stung": true}}, {"add": {"component_groups": ["ptd_lmhc:start_die_timer"]}}]}, "ptd_lmhc:die": {"add": {"component_groups": ["ptd_lmhc:dead"]}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:behavior.melee_box_attack": {"priority": 2, "attack_once": false, "speed_multiplier": 3, "on_attack": {"filters": {"test": "bool_property", "subject": "self", "domain": "ptd_lmhc:already_stung", "value": false}, "event": "ptd_lmhc:start_die_timer", "target": "self"}}, "minecraft:on_target_acquired": {"event": "ptd_lmhc:angry", "target": "self"}, "minecraft:attack": {"damage": 3, "effect_name": "poison", "effect_duration": 5}, "minecraft:behavior.nearest_attackable_target": {"priority": 0, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}]}, "max_dist": 16}]}, "minecraft:timer": {"time": 15, "looping": false, "time_down_event": {"event": "ptd_lmhc:despawn", "target": "self"}}, "minecraft:behavior.random_hover": {"priority": 12, "xz_dist": 8, "y_dist": 8, "y_offset": -1, "interval": 1, "hover_height": [1, 4]}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0}, "minecraft:balloonable": {"mass": 0.5}, "minecraft:behavior.float": {"priority": 19}, "minecraft:type_family": {"family": ["bee", "bee_dragon_bee", "mob", "arthropod"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:collision_box": {"width": 0.55, "height": 0.5}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:game_event_movement_tracking": {"emit_flap": true}, "minecraft:follow_range": {"value": 1024}, "minecraft:damage_sensor": {"triggers": {"cause": "fall", "deals_damage": "no"}}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.3}, "minecraft:flying_speed": {"value": 0.15}, "minecraft:navigation.hover": {"can_path_over_water": true, "can_sink": false, "can_pass_doors": false, "can_path_from_air": true, "avoid_water": true, "avoid_damage_blocks": true, "avoid_sun": false}, "minecraft:movement.hover": {}, "minecraft:jump.static": {}, "minecraft:can_fly": {}, "minecraft:health": {"value": 10, "max": 10}, "minecraft:nameable": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:ambient_sound_interval": {"event_name": "ambient", "range": 2, "value": 1}}}}