import { Entity, Player, Vector3 } from '@minecraft/server';
import { excludedEntitiesPlayerForAll } from '../../utilities/entityQueries';
import { onEatMeal } from '../events';

/**
 * Constants for Dinnerbone Party effect
 */
const SEARCH_RADIUS: number = 32; // Range to search for mobs around player
const MAX_MOBS_TO_TRANSFORM: number = 20; // Maximum number of mobs to transform

/**
 * Event 67: Dinnerbone Party - Names all nearby mobs "Dinnerbone" which turns them upside down
 * Uses a Minecraft Easter egg where entities named "Dinnerbone" are rendered upside down
 *
 * @param player - The player who triggered the event
 */
export function event67(player: Player): void {
  try {
    const dimension = player.dimension;
    const playerPos: Vector3 = player.location;

    // Get all entities within the search radius
    const nearbyEntities: Entity[] = [
      ...dimension.getEntities({
        location: playerPos,
        maxDistance: SEARCH_RADIUS,
        excludeFamilies: ['inanimate', 'player'], // Explicitly exclude players
        ...excludedEntitiesPlayerForAll
      })
    ];

    // Counter for transformed mobs
    let transformedCount = 0;

    if (nearbyEntities.length === 0) {
      onEatMeal(player);
      return;
    }

    // Transform mobs, but limit to a maximum to prevent performance issues
    for (const entity of nearbyEntities) {
      // Skip if entity is not valid, already has the name tag, or is a player
      if (!entity || entity.nameTag === 'Dinnerbone' || entity instanceof Player) {
        continue;
      }

      // Apply Dinnerbone name tag to turn entity upside down
      entity.nameTag = 'Dinnerbone';

      // Play particle effect
      dimension.spawnParticle('minecraft:cauldron_explosion_emitter', entity.location);

      // Play sound effect
      dimension.playSound('mob.endermen.portal', entity.location);

      // Increment counter
      transformedCount++;

      // Stop if we've transformed enough mobs
      if (transformedCount >= MAX_MOBS_TO_TRANSFORM) {
        break;
      }
    }
  } catch (error) {
    console.warn(`Failed to execute event 67: ${error}`);
  }
  return;
}
