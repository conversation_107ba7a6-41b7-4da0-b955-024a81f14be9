import { system, world, StructureRotation, StructureMirrorAxis, StructureAnimationMode } from '@minecraft/server';
/**
 * Event 41. Instant House: Spawns a fully built base with beds, chests, crafting tables, and furnaces
 * @param player The player to spawn the house for
 */
export function event41(player) {
    try {
        // Get the player's position
        const playerPosition = player.location;
        // Find a suitable position for the house (5 blocks in front of the player)
        const playerRotY = player.getRotation().y;
        const radians = playerRotY * (Math.PI / 180);
        // Calculate position 5 blocks in front of player
        const spawnPos = {
            x: playerPosition.x - Math.sin(radians) * 5,
            y: playerPosition.y,
            z: playerPosition.z + Math.cos(radians) * 5
        };
        // Round coordinates to ensure proper placement
        spawnPos.x = Math.floor(spawnPos.x);
        spawnPos.y = Math.floor(spawnPos.y);
        spawnPos.z = Math.floor(spawnPos.z);
        // Get the dimension the player is in
        const dimension = player.dimension;
        // Check for solid ground and snap to it
        const maxSearchDistance = 10; // Maximum blocks to search down for ground
        let foundGround = false;
        let groundY = spawnPos.y;
        // First check if we're already on solid ground
        let blockBelow = dimension.getBlock({ x: spawnPos.x, y: spawnPos.y - 1, z: spawnPos.z });
        if (blockBelow && isBlockSolid(blockBelow.permutation)) {
            foundGround = true;
            groundY = spawnPos.y;
        }
        else {
            // Search downward for solid ground
            for (let y = spawnPos.y; y > spawnPos.y - maxSearchDistance; y--) {
                blockBelow = dimension.getBlock({ x: spawnPos.x, y: y - 1, z: spawnPos.z });
                if (blockBelow && isBlockSolid(blockBelow.permutation)) {
                    foundGround = true;
                    groundY = y;
                    break;
                }
            }
        }
        // If no ground found, search upward (in case player is underground)
        if (!foundGround) {
            for (let y = spawnPos.y; y < spawnPos.y + maxSearchDistance; y++) {
                blockBelow = dimension.getBlock({ x: spawnPos.x, y: y - 1, z: spawnPos.z });
                if (blockBelow && isBlockSolid(blockBelow.permutation)) {
                    foundGround = true;
                    groundY = y;
                    break;
                }
            }
        }
        // Update spawn position Y to the ground level
        spawnPos.y = groundY;
        // If still no solid ground found, notify player and return
        if (!foundGround) {
            player.sendMessage("§cCouldn't find solid ground to place the house. Try a different location.");
            return;
        }
        // Available house types
        const houseTypes = ['hobbit', 'modern', 'western'];
        // Randomly select a house type
        const randomHouseType = houseTypes[Math.floor(Math.random() * houseTypes.length)];
        // Structure parameters
        const structureId = `ptd:lm_hc/event41/${randomHouseType}`;
        // Send message to player about which house type they got
        player.sendMessage(`§aYou received a §6${randomHouseType}§a style house!`);
        // Determine structure rotation based on player's direction
        // Convert player's Y rotation to a structure rotation
        // The structure should face the player, so we need to rotate it 180 degrees from the player's direction
        let rotation;
        // Normalize the player rotation to 0-360 degrees
        const normalizedRotY = ((playerRotY % 360) + 360) % 360;
        // Determine the appropriate structure rotation
        // Assuming the structure's default orientation (None) is facing south
        if (normalizedRotY >= 315 || normalizedRotY < 45) {
            // Player facing north (0 degrees), structure should face south
            rotation = StructureRotation.None; // South (0 degrees)
        }
        else if (normalizedRotY >= 45 && normalizedRotY < 135) {
            // Player facing east (90 degrees), structure should face west
            rotation = StructureRotation.Rotate90; // West (90 degrees)
        }
        else if (normalizedRotY >= 135 && normalizedRotY < 225) {
            // Player facing south (180 degrees), structure should face north
            rotation = StructureRotation.Rotate180; // North (180 degrees)
        }
        else {
            // Player facing west (270 degrees), structure should face east
            rotation = StructureRotation.Rotate270; // East (270 degrees)
        }
        // Use the StructureManager API to place the structure
        world.structureManager.place(structureId, dimension, spawnPos, {
            rotation: rotation,
            mirror: StructureMirrorAxis.None,
            animationMode: StructureAnimationMode.None,
            includeEntities: true,
            waterlogged: false
        });
        // Play a sound effect
        dimension.playSound('block.wood.place', playerPosition, { volume: 1.0, pitch: 1.0 });
        // Show particles
        system.runTimeout(() => {
            for (let i = 0; i < 50; i++) {
                const particlePos = {
                    x: spawnPos.x + Math.random() * 6 - 3,
                    y: spawnPos.y + Math.random() * 4,
                    z: spawnPos.z + Math.random() * 6 - 3
                };
                dimension.spawnParticle('minecraft:villager_happy', particlePos);
            }
        }, 40);
    }
    catch (error) {
        console.warn(`Error in event41: ${error}`);
    }
    return;
}
/**
 * Check if a block permutation is solid (can be built upon)
 * @param permutation The block permutation to check
 * @returns True if the block is solid, false otherwise
 */
function isBlockSolid(permutation) {
    // List of non-solid block types
    const nonSolidBlocks = [
        'minecraft:air',
        'minecraft:water',
        'minecraft:lava',
        'minecraft:fire',
        'minecraft:tall_grass',
        'minecraft:grass',
        'minecraft:seagrass',
        'minecraft:tall_seagrass',
        'minecraft:kelp',
        'minecraft:bamboo',
        'minecraft:flower',
        'minecraft:leaves',
        'minecraft:snow',
        'minecraft:vine'
    ];
    const blockType = permutation.type;
    const typeId = blockType.id;
    // Check if the block is in the non-solid list
    for (const nonSolidBlock of nonSolidBlocks) {
        if (typeId.includes(nonSolidBlock)) {
            return false;
        }
    }
    return true;
}
