{
  "format_version": "1.21.0",
  "minecraft:entity": {
    "description": {
      "identifier": "ptd_lmhc:zombie",
      "spawn_category": "monster",
      "is_spawnable": true,
      "is_summonable": true
    },
    "component_groups": {
      "ptd_lmhc:rising": {
        "minecraft:timer": {
          "looping": false,
          "time": 1,
          "time_down_event": {
            "event": "ptd_lmhc:active"
          }
        }
      },
      "ptd_lmhc:active": {
        "minecraft:behavior.random_stroll": {
          "priority": 7,
          "speed_multiplier": 1
        },
        "minecraft:behavior.look_at_player": {
          "priority": 8,
          "look_distance": 6,
          "probability": 0.02
        },
        "minecraft:behavior.random_look_around": {
          "priority": 9
        },
        "minecraft:behavior.hurt_by_target": {
          "priority": 1,
          "entity_types": [
            {
              "filters": {
                "test": "is_family",
                "subject": "other",
                "operator": "!=",
                "value": "breeze"
              }
            }
          ]
        },
        "minecraft:behavior.nearest_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "within_radius": 25.0,
          "must_see_forget_duration": 17.0,
          "entity_types": [
            {
              "filters": {
                "any_of": [
                  { "test": "is_family", "subject": "other", "value": "player" },
                  { "test": "is_family", "subject": "other", "value": "snowgolem" },
                  { "test": "is_family", "subject": "other", "value": "irongolem" }
                ]
              },
              "max_dist": 35
            },
            {
              "filters": {
                "any_of": [
                  { "test": "is_family", "subject": "other", "value": "villager" },
                  { "test": "is_family", "subject": "other", "value": "wandering_trader" }
                ]
              },
              "max_dist": 35,
              "must_see": false
            },
            {
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "baby_turtle" },
                  { "test": "in_water", "subject": "other", "operator": "!=", "value": true }
                ]
              },
              "max_dist": 35
            }
          ]
        }
      },
      "ptd_lmhc:bob_the_zombie": {
        "minecraft:equipment": {
          "table": "loot_tables/ptd/lm_hc/event18.json"
        }
      },
      "minecraft:look_to_start_drowned_transformation": {
        "minecraft:environment_sensor": {
          "triggers": {
            "filters": {
              "test": "is_underwater",
              "subject": "self",
              "operator": "==",
              "value": true
            },
            "event": "minecraft:start_transforming"
          }
        }
      },
      "minecraft:start_drowned_transformation": {
        "minecraft:environment_sensor": {
          "triggers": {
            "filters": {
              "test": "is_underwater",
              "subject": "self",
              "operator": "==",
              "value": false
            },
            "event": "minecraft:stop_transforming"
          }
        },
        "minecraft:timer": {
          "looping": false,
          "time": 30,
          "time_down_event": {
            "event": "minecraft:convert_to_drowned"
          }
        }
      },

      "minecraft:convert_to_drowned": {
        "minecraft:transformation": {
          "into": "minecraft:drowned<minecraft:as_adult>",
          "transformation_sound": "convert_to_drowned",
          "drop_equipment": true,
          "delay": {
            "value": 15
          }
        },
        "minecraft:is_shaking": {}
      },
      "minecraft:can_have_equipment": {
        "minecraft:equipment": {
          "table": "loot_tables/entities/zombie_equipment.json"
        },
        "minecraft:nameable": {
          "allow_name_tag_renaming": true,
          "always_show": true
        }
      },
      "minecraft:can_break_doors": {
        "minecraft:annotation.break_door": {}
      }
    },

    "components": {
      "minecraft:is_hidden_when_invisible": {},
      "minecraft:nameable": {},

      // Zombie Components
      "minecraft:type_family": {
        "family": ["zombie", "undead", "monster", "mob"]
      },
      "minecraft:equip_item": {
        "excluded_items": [
          {
            "item": "minecraft:banner:15"
          }
        ]
      },
      "minecraft:collision_box": {
        "width": 0.6,
        "height": 1.9
      },
      "minecraft:burns_in_daylight": {},
      "minecraft:movement.basic": {},
      "minecraft:navigation.walk": {
        "is_amphibious": true,
        "can_pass_doors": true,
        "can_walk": true,
        "can_break_doors": true
      },
      "minecraft:jump.static": {},
      "minecraft:can_climb": {},
      "minecraft:health": {
        "value": 20,
        "max": 20
      },
      "minecraft:hurt_on_condition": {
        "damage_conditions": [
          {
            "filters": { "test": "in_lava", "subject": "self", "operator": "==", "value": true },
            "cause": "lava",
            "damage_per_tick": 4
          }
        ]
      },

      "minecraft:experience_reward": {
        "on_death": "query.last_hit_by_player ? 5 + (query.equipment_count * Math.Random(1,3)) : 0"
      },
      "minecraft:movement": {
        "value": 0.23
      },
      "minecraft:rideable": {
        "seat_count": 1,
        "family_types": ["zombie"],
        "seats": {
          "position": [0.0, 1.1, -0.35],
          "lock_rider_rotation": 0
        }
      },
      "minecraft:behavior.mount_pathing": {
        "priority": 2,
        "speed_multiplier": 1.25,
        "target_dist": 0.0,
        "track_target": true
      },
      "minecraft:breathable": {
        "total_supply": 15,
        "suffocate_time": 0,
        "breathes_air": true,
        "breathes_water": true
      },
      "minecraft:attack": {
        "damage": 3
      },
      "minecraft:loot": {
        "table": "loot_tables/entities/zombie.json"
      },
      "minecraft:shareables": {
        "singular_pickup": true,
        "items": [
          {
            "item": "minecraft:netherite_sword",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 0
          },
          {
            "item": "minecraft:diamond_sword",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 1
          },
          {
            "item": "minecraft:iron_sword",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 2
          },
          {
            "item": "minecraft:stone_sword",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 3
          },
          {
            "item": "minecraft:golden_sword",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 4
          },
          {
            "item": "minecraft:wooden_sword",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 5
          },
          {
            "item": "minecraft:netherite_helmet",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 0
          },
          {
            "item": "minecraft:diamond_helmet",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 1
          },
          {
            "item": "minecraft:iron_helmet",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 2
          },
          {
            "item": "minecraft:chainmail_helmet",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 3
          },
          {
            "item": "minecraft:golden_helmet",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 4
          },
          {
            "item": "minecraft:leather_helmet",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 5
          },
          {
            "item": "minecraft:turtle_helmet",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 6
          },
          {
            "item": "minecraft:skull:0",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 7
          },
          {
            "item": "minecraft:skull:1",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 7
          },
          {
            "item": "minecraft:carved_pumpkin",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 7
          },
          {
            "item": "minecraft:netherite_chestplate",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 0
          },
          {
            "item": "minecraft:diamond_chestplate",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 1
          },
          {
            "item": "minecraft:iron_chestplate",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 2
          },
          {
            "item": "minecraft:chainmail_chestplate",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 3
          },
          {
            "item": "minecraft:golden_chestplate",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 4
          },
          {
            "item": "minecraft:leather_chestplate",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 5
          },
          {
            "item": "minecraft:netherite_leggings",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 0
          },
          {
            "item": "minecraft:diamond_leggings",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 1
          },
          {
            "item": "minecraft:iron_leggings",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 2
          },
          {
            "item": "minecraft:chainmail_leggings",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 3
          },
          {
            "item": "minecraft:golden_leggings",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 4
          },
          {
            "item": "minecraft:leather_leggings",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 5
          },
          {
            "item": "minecraft:netherite_boots",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 0
          },
          {
            "item": "minecraft:diamond_boots",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 1
          },
          {
            "item": "minecraft:iron_boots",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 2
          },
          {
            "item": "minecraft:chainmail_boots",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 3
          },
          {
            "item": "minecraft:golden_boots",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 4
          },
          {
            "item": "minecraft:leather_boots",
            "want_amount": 1,
            "surplus_amount": 1,
            "priority": 5
          }
        ]
      },

      "minecraft:environment_sensor": {
        "triggers": {
          "filters": {
            "test": "is_underwater",
            "operator": "==",
            "value": true
          },
          "event": "minecraft:start_transforming"
        }
      },

      "minecraft:despawn": {
        "despawn_from_distance": {}
      },

      // Zombie Behaviors
      "minecraft:behavior.equip_item": {
        "priority": 2
      },
      "minecraft:behavior.melee_box_attack": {
        "can_spread_on_fire": true,
        "priority": 3
      },
      "minecraft:behavior.stomp_turtle_egg": {
        "priority": 4,
        "speed_multiplier": 1,
        "search_range": 10,
        "search_height": 2,
        "goal_radius": 1.14,
        "interval": 20
      },
      "minecraft:behavior.pickup_items": {
        "priority": 6,
        "max_dist": 3,
        "goal_radius": 2,
        "speed_multiplier": 1.0,
        "pickup_based_on_chance": true,
        "can_pickup_any_item": true,
        "excluded_items": ["minecraft:glow_ink_sac"]
      },
      "minecraft:physics": {},
      "minecraft:pushable": {
        "is_pushable": true,
        "is_pushable_by_piston": true
      },
      "minecraft:conditional_bandwidth_optimization": {}
    },

    "events": {
      "minecraft:entity_spawned": {
        "sequence": [
          {
            "add": {
              "component_groups": ["ptd_lmhc:rising"]
            }
          },
          {
            "randomize": [
              {
                "weight": 10,
                "add": {
                  "component_groups": ["minecraft:can_break_doors"]
                }
              },
              {
                "weight": 90
              }
            ]
          }
        ]
      },
      "ptd_lmhc:active": {
        "remove": {
          "component_groups": ["ptd_lmhc:rising"]
        },
        "add": {
          "component_groups": ["ptd_lmhc:active"]
        }
      },

      "ptd_lmhc:as_bob": {
        "add": {
          "component_groups": ["ptd_lmhc:bob_the_zombie"]
        }
      },
      "minecraft:start_transforming": {
        "add": {
          "component_groups": ["minecraft:start_drowned_transformation"]
        },
        "remove": {
          "component_groups": ["minecraft:look_to_start_drowned_transformation"]
        }
      },
      "minecraft:stop_transforming": {
        "add": {
          "component_groups": ["minecraft:look_to_start_drowned_transformation"]
        },
        "remove": {
          "component_groups": ["minecraft:start_drowned_transformation"]
        }
      },
      "minecraft:convert_to_drowned": {
        "add": {
          "component_groups": ["minecraft:convert_to_drowned"]
        }
      }
    }
  }
}
