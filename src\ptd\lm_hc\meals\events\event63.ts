import { Player, Vector3 } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { EntityQuantityConfig, spawnEntitiesWithInterval } from '../../utilities/summonEntity';

/**
 * Event 63: Sheep Party - Spawns colorful sheep named _jeb that display rainbow color patterns
 * Sheep appear with particles and sounds in random locations around the player
 *
 * @param player - The player who triggered the event
 */
export function event63(player: Player): void {
  try {
    // Configure sheep entity type and quantity
    const entityConfigs: EntityQuantityConfig[] = [{ entityId: 'minecraft:sheep', count: 15 }];

    /**
     * Gets a random spawn location for sheep, ensuring there's air
     * @returns Valid spawn Vector3 position or undefined if none found
     */
    const getSpawnLocation = (): Vector3 | undefined => {
      // Get a random location using the utility function
      return getRandomLocation(
        player.location,
        player.dimension,
        4, // Base offset (minimum distance from player)
        8, // Additional random offset
        1, // Y offset (slightly above ground)
        true // Check for air blocks
      );
    };

    // Start spawning sheep with a 10 tick delay between each
    spawnEntitiesWithInterval(
      player.dimension,
      entityConfigs,
      getSpawnLocation,
      4, // Delay between spawns in ticks
      (sheep) => {
        // Name the sheep "_jeb" to make them display rainbow colors
        if (sheep && sheep) {
          // Set the sheep's name tag
          sheep.nameTag = 'jeb_';

          // Play the cauldron explosion particle at the sheep's location
          player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', sheep.location);

          // Play enderman teleport sound for effect
          player.dimension.playSound('mob.endermen.portal', sheep.location);
        }
      }
    ).catch((error) => {
      console.warn(`Error in event63 spawning process: ${error}`);
    });
  } catch (error) {
    console.warn(`Failed to execute event 63: ${error}`);
  }
  return;
}
