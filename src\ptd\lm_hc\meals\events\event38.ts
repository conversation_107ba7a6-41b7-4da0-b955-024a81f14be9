import { Player, EntityInventoryComponent, EntityComponentTypes } from '@minecraft/server';

/**
 * Hotbar Vanish - Permanently removes all items from the player's hotbar
 * @param player The player whose hotbar will vanish
 */
export function event38(player: Player): void {
  try {
    // Get the player's inventory component
    const inventory = player.getComponent(EntityComponentTypes.Inventory) as EntityInventoryComponent;

    if (!inventory || !inventory.container) {
      console.warn('Failed to get inventory component or container for player');
      return;
    }

    const hotbarSize = 9; // Minecraft hotbar has 9 slots

    // Visual effect to indicate the hotbar vanishing
    player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', player.location);
    player.dimension.playSound('mob.endermen.portal', player.location);

    // Clear hotbar items
    for (let slot = 0; slot < hotbarSize; slot++) {
      const item = inventory.container.getItem(slot);
      if (item) {
        inventory.container.setItem(slot, undefined);
      }
    }

    // Display message to the player
    player.onScreenDisplay.setActionBar('§4Your hotbar has vanished!');
  } catch (error) {
    console.warn(`Error in event38 (Hot Bar Vanish): ${error}`);
  }
  return;
}
