{"format_version": "1.20.80", "minecraft:entity": {"description": {"identifier": "ptd_lmhc:guidebook", "is_spawnable": false, "is_summonable": true, "properties": {"ptd_lmhc:pages": {"client_sync": true, "type": "int", "default": 0, "range": [0, 22]}, "ptd_lmhc:achievements_page7": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page8": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page9": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page10": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page11": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page12": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page13": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page14": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page15": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page16": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page17": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page18": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page19": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page20": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}, "ptd_lmhc:achievements_page21": {"client_sync": true, "type": "int", "default": 0, "range": [0, 31]}}}, "component_groups": {"ptd_lmhc:despawn": {"minecraft:instant_despawn": {}}}, "events": {"ptd_lmhc:despawn": {"add": {"component_groups": ["ptd_lmhc:despawn"]}}, "ptd_lmhc:previous_page": {"sequence": [{"filters": {"test": "int_property", "domain": "ptd_lmhc:pages", "value": 0}, "set_property": {"ptd_lmhc:pages": 22}, "queue_command": {"command": ["playsound item.book.page_turn @p ~~~"]}}, {"filters": {"test": "int_property", "domain": "ptd_lmhc:pages", "operator": ">", "value": 0}, "set_property": {"ptd_lmhc:pages": "q.property('ptd_lmhc:pages') - 1"}, "queue_command": {"command": ["playsound item.book.page_turn @p ~~~"]}}]}, "ptd_lmhc:next_page": {"sequence": [{"filters": {"test": "int_property", "domain": "ptd_lmhc:pages", "value": 22}, "set_property": {"ptd_lmhc:pages": 0}, "queue_command": {"command": ["playsound item.book.page_turn @p ~~~"]}}, {"filters": {"test": "int_property", "domain": "ptd_lmhc:pages", "operator": "<", "value": 22}, "set_property": {"ptd_lmhc:pages": "q.property('ptd_lmhc:pages') + 1"}, "queue_command": {"command": ["playsound item.book.page_turn @p ~~~"]}}]}}, "components": {"minecraft:type_family": {"family": ["inanimate", "guidebook"]}, "minecraft:collision_box": {"height": 0.01, "width": 0.01}, "minecraft:breathable": {"total_supply": 15, "breathes_air": true, "breathes_water": true, "breathes_lava": true, "breathes_solids": true, "generates_bubbles": false}, "minecraft:custom_hit_test": {"hitboxes": [{"width": 0.2, "height": 1.9, "pivot": [-0.5, 0.95, 0]}, {"width": 0.2, "height": 1.9, "pivot": [-0.3, 0.95, 0]}, {"width": 0.2, "height": 1.9, "pivot": [-0.1, 0.95, 0]}, {"width": 0.2, "height": 1.9, "pivot": [0.1, 0.95, 0]}, {"width": 0.2, "height": 1.9, "pivot": [0.3, 0.95, 0]}, {"width": 0.2, "height": 1.9, "pivot": [0.5, 0.95, 0]}, {"width": 0.2, "height": 1.9, "pivot": [0.6, 0.95, 0]}]}, "minecraft:fire_immune": {}, "minecraft:health": {"value": 10, "max": 10}, "minecraft:interact": {"interactions": [{"interact_text": "action.hint.interact.ptd_lmhc.guidebook.previous_page", "on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_sneaking", "subject": "other", "value": true}]}, "event": "ptd_lmhc:previous_page", "target": "self"}, "swing": true}, {"interact_text": "action.hint.interact.ptd_lmhc.guidebook.next_page", "on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_sneaking", "subject": "other", "value": false}]}, "event": "ptd_lmhc:next_page", "target": "self"}, "swing": true}]}, "minecraft:damage_sensor": {"triggers": {"cause": "all", "deals_damage": false, "on_damage": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "event": "ptd_lmhc:despawn", "target": "self"}}}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}}}}