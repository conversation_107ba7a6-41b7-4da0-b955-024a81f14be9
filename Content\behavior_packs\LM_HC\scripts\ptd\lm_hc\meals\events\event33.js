import { BlockPermutation, system } from '@minecraft/server';
import { getRandomInt } from '../../utilities/rng';
import { getRandomLocation } from '../../utilities/vector3';
/**
 * Diamond Meteor: Spawns clusters of diamond ore blocks that crash from the sky around the player.
 * Creates meteor-like impacts with particle effects, explosions, and sounds at each landing site.
 * Each meteor falls from random angles for a more dynamic and realistic meteor shower appearance.
 * Diamond ore clusters embed themselves into the ground for a realistic impact crater effect.
 *
 * @param player - The player around whom the diamond meteors will crash
 * @remarks
 * - Spawns 5-15 diamond ore clusters (random amount)
 * - Each cluster contains 5-10 diamond ore blocks embedded in the ground
 * - Meteors fall from random angles and heights
 * - Clusters spawn at random coordinates near the player
 * - Spawning occurs with delay intervals for a meteor shower effect
 * - Impact effects include particles, explosions, and sounds
 * - Meteors have realistic falling animation with particle trails
 *
 * @throws Logs a warning if diamond meteor spawning fails
 * @throws Logs a warning if the overall event execution fails
 */
export async function event33(player) {
    try {
        // Get diamond ore block permutation
        const diamondOre = BlockPermutation.resolve('minecraft:diamond_ore');
        if (!diamondOre) {
            throw new Error('Failed to get diamond ore block permutation');
        }
        // More meteors and wider spread for a more dramatic effect
        const meteorCount = getRandomInt(5, 12);
        let spawned = 0;
        // Constants for meteor configuration
        const startHeight = getRandomInt(35, 50); // Higher starting point for more dramatic effect
        const SPAWN_DELAY = getRandomInt(5, 40); // Delay between each meteor spawn (in ticks, 20 ticks = 1 second)
        // Initial apocalyptic sound effects for dramatic start
        player.dimension.playSound('mob.wither.spawn', player.location, { volume: 1.0, pitch: 0.7 });
        // Thunder sound near player when each meteor spawns
        const playThunderInterval = system.runInterval(() => {
            if (spawned >= meteorCount) {
                system.clearRun(playThunderInterval);
                return;
            }
            player.dimension.playSound('ambient.weather.thunder', player.location, { volume: 1.0, pitch: 0.5 });
        }, SPAWN_DELAY);
        // Start spawning meteors with delay between each
        const spawnInterval = system.runInterval(() => {
            if (spawned >= meteorCount) {
                system.clearRun(spawnInterval);
                return;
            }
            // Get landing position around player with wider spread
            const targetPos = getRandomLocation(player.location, player.dimension, 6, // minimum distance from player
            getRandomInt(5, 18), // much wider random offset
            0, // initial y-offset, will be adjusted
            false // don't check for air block yet
            );
            // Find the actual landing position by checking for solid blocks
            let landingPos;
            if (targetPos) {
                // Start from high up and scan down
                for (let y = targetPos.y + 50; y > -64; y--) {
                    const checkPos = { x: targetPos.x, y, z: targetPos.z };
                    const block = player.dimension.getBlock(checkPos);
                    if (block && !(block.isAir || block.isLiquid)) {
                        landingPos = { x: targetPos.x, y: y + 1, z: targetPos.z };
                        break;
                    }
                }
            }
            if (landingPos) {
                try {
                    // Create angle offsets for random meteor trajectory
                    // These offsets will determine the direction from which the meteor falls
                    const angleOffset = {
                        x: (Math.random() * 2 - 1) * 30, // Random value between -30 and 30
                        y: startHeight, // Height is always positive
                        z: (Math.random() * 2 - 1) * 30 // Random value between -30 and 30
                    };
                    // Starting position with angle offset
                    const startPos = {
                        x: landingPos.x + angleOffset.x,
                        y: landingPos.y + angleOffset.y,
                        z: landingPos.z + angleOffset.z
                    };
                    // Calculate vector for gradual movement (how much to move each tick)
                    const FALL_STEPS = 20; // Reduced steps for faster fall
                    const moveStep = {
                        x: (landingPos.x - startPos.x) / FALL_STEPS,
                        y: (landingPos.y - startPos.y) / FALL_STEPS,
                        z: (landingPos.z - startPos.z) / FALL_STEPS
                    };
                    // Current position of the falling meteor
                    const currentPos = { ...startPos };
                    // Create meteor trail effect while it's "falling"
                    const trailEffect = system.runInterval(() => {
                        try {
                            // Update position along the trajectory
                            currentPos.x += moveStep.x;
                            currentPos.y += moveStep.y;
                            currentPos.z += moveStep.z;
                            // Enhanced particle effects for faster, more intense trail
                            for (let i = 0; i < 3; i++) {
                                const randomOffset = {
                                    x: currentPos.x + (Math.random() - 0.5),
                                    y: currentPos.y + (Math.random() - 0.5),
                                    z: currentPos.z + (Math.random() - 0.5)
                                };
                                player.dimension.spawnParticle('minecraft:large_explosion', randomOffset);
                                player.dimension.spawnParticle('minecraft:lava_particle', randomOffset);
                                player.dimension.spawnParticle('minecraft:basic_flame_particle', randomOffset);
                                player.dimension.spawnParticle('minecraft:blue_flame_particle', randomOffset);
                            }
                            // Check for block collision
                            const currentBlock = player.dimension.getBlock(currentPos);
                            if (currentBlock && !(currentBlock.isAir || currentBlock.isLiquid)) {
                                system.clearRun(trailEffect);
                                // Adjust final position to be just above the hit block
                                currentPos.y = Math.ceil(currentPos.y);
                                // Create explosion BEFORE placing diamonds - this creates space for the diamonds
                                const explosionOptions = {
                                    breaksBlocks: true, // Create actual craters in the terrain
                                    causesFire: false, // No fire from explosion
                                    allowUnderwater: true, // Work underwater too
                                    source: player // Player is the source (for game mechanics)
                                };
                                // Create the explosion with moderate power
                                player.dimension.createExplosion(landingPos, 7, explosionOptions);
                                // Wait a small delay for explosion to finish before placing diamonds
                                system.runTimeout(() => {
                                    // Spawn larger diamond ore cluster at the bottom of the crater
                                    const clusterSize = getRandomInt(15, 25); // Significantly larger cluster size
                                    const craterDepth = {
                                        x: landingPos.x,
                                        y: landingPos.y - 3, // Position cluster 3 blocks lower in the crater
                                        z: landingPos.z
                                    };
                                    spawnEmbeddedMeteorCluster(player, diamondOre, craterDepth, clusterSize);
                                    // Impact effects after explosion
                                    player.dimension.spawnParticle('minecraft:huge_explosion_emitter', landingPos);
                                    // Enhanced impact sounds
                                    player.dimension.playSound('random.explode', landingPos, { volume: 25.0, pitch: 0.6 });
                                    // Diamond sparkle effects at impact site - more sparkles
                                    for (let i = 0; i < 20; i++) {
                                        const sparklePos = {
                                            x: landingPos.x + (Math.random() * 2 - 1) * 3, // Wider sparkle area
                                            y: landingPos.y + Math.random() * 2,
                                            z: landingPos.z + (Math.random() * 2 - 1) * 3 // Wider sparkle area
                                        };
                                        player.dimension.spawnParticle('minecraft:blue_flame_particle', sparklePos);
                                    }
                                }, 5); // Short delay for dramatic effect after explosion
                            }
                        }
                        catch (error) {
                            console.warn(`Error in meteor trail effect: ${error}`);
                            system.clearRun(trailEffect);
                        }
                    }, 1); // Update every tick for smooth animation
                    spawned++;
                }
                catch (error) {
                    console.warn(`Failed to spawn diamond meteor: ${error}`);
                }
            }
        }, SPAWN_DELAY);
    }
    catch (error) {
        console.warn(`Failed to execute Diamond Meteor event: ${error}`);
    }
    return;
}
/**
 * Spawns a cluster of diamond ore blocks embedded into the ground at the specified location
 * Creates a crater-like formation with diamond ore blocks
 *
 * @param player - The player for dimension reference
 * @param blockPerm - The block permutation to use
 * @param center - The center position of the cluster
 * @param count - How many blocks to include in the cluster
 */
function spawnEmbeddedMeteorCluster(player, blockPerm, center, count) {
    // Expanded offsets for larger clusters starting from crater bottom
    const offsets = [
        // Surface level - expanded radius
        { x: 0, y: 0, z: 0 }, // center
        ...Array.from({ length: 8 }, (_, i) => {
            const angle = (i * Math.PI) / 4;
            return { x: Math.round(2 * Math.cos(angle)), y: 0, z: Math.round(2 * Math.sin(angle)) };
        }),
        ...Array.from({ length: 12 }, (_, i) => {
            const angle = (i * Math.PI) / 6;
            return { x: Math.round(3 * Math.cos(angle)), y: 0, z: Math.round(3 * Math.sin(angle)) };
        }),
        // Underground level 1 (-1)
        ...Array.from({ length: 8 }, (_, i) => {
            const angle = (i * Math.PI) / 4;
            return { x: Math.round(2 * Math.cos(angle)), y: -1, z: Math.round(2 * Math.sin(angle)) };
        }),
        { x: 0, y: -1, z: 0 },
        // Underground level 2 (-2)
        ...Array.from({ length: 6 }, (_, i) => {
            const angle = (i * Math.PI) / 3;
            return { x: Math.round(2 * Math.cos(angle)), y: -2, z: Math.round(2 * Math.sin(angle)) };
        }),
        { x: 0, y: -2, z: 0 },
        // Underground level 3 (-3)
        ...Array.from({ length: 6 }, (_, i) => {
            const angle = (i * Math.PI) / 3;
            return { x: Math.round(1.5 * Math.cos(angle)), y: -3, z: Math.round(1.5 * Math.sin(angle)) };
        }),
        { x: 0, y: -3, z: 0 },
        // Deep underground (-4 to -6)
        { x: 0, y: -4, z: 0 },
        { x: 1, y: -4, z: 0 },
        { x: -1, y: -4, z: 0 },
        { x: 0, y: -4, z: 1 },
        { x: 0, y: -4, z: -1 },
        { x: 0, y: -5, z: 0 },
        { x: 0, y: -6, z: 0 },
        // Extended surface radius
        ...Array.from({ length: 8 }, (_, i) => {
            const angle = (i * Math.PI) / 4;
            return { x: Math.round(4 * Math.cos(angle)), y: -1, z: Math.round(4 * Math.sin(angle)) };
        })
    ];
    // Shuffle and take the first 'count' offsets
    const shuffledOffsets = offsets.sort(() => 0.5 - Math.random()).slice(0, count);
    // Then place the diamond ore blocks according to the offsets
    // No need to create a crater since the explosion already did that
    for (const offset of shuffledOffsets) {
        const pos = {
            x: Math.floor(center.x) + offset.x,
            y: Math.floor(center.y) + offset.y,
            z: Math.floor(center.z) + offset.z
        };
        const block = player.dimension.getBlock(pos);
        // Make sure we don't replace bedrock or other important blocks
        if (block &&
            (block.isAir ||
                block.typeId === 'minecraft:grass' ||
                block.typeId === 'minecraft:dirt' ||
                block.typeId === 'minecraft:stone' ||
                block.typeId.includes('leaves') ||
                block.typeId.includes('flower') ||
                block.typeId.includes('grass') ||
                block.typeId.includes('tallgrass') ||
                block.typeId.includes('gravel') ||
                block.typeId.includes('sand'))) {
            block.setPermutation(blockPerm);
            // Add delayed sparkle effect on each diamond ore for a "settling" effect
            system.runTimeout(() => {
                player.dimension.spawnParticle('minecraft:blue_flame_particle', pos);
            }, getRandomInt(5, 15));
        }
    }
    return;
}
