﻿{
  "format_version": "1.20.50",
  "minecraft:attachable": {
    "description": {
      "identifier": "ptd_lmhc:cheese_shell_soup",
      "materials": {
        "default": "entity_alphatest"
      },
      "textures": {
        "default": "textures/ptd/lm_hc/items/food/cheese_shell_soup",
        "enchanted": "textures/misc/enchanted_item_glint"
      },
      "geometry": {
        "default": "geometry.cheese_shell_soup"
      },
      "animations": {
        "hold_first_person": "animation.food.cheese_shell_soup.hold_first_person",
        "hold_third_person": "animation.food.cheese_shell_soup.hold_third_person",
        "eat": "animation.food.cheese_shell_soup.eat",
        "eat_controller": "controller.animation.food.eat",
        "general": "controller.animation.food.general"
      },
      "scripts": {
        "animate": ["general", "eat_controller"]
      },
      "render_controllers": ["controller.render.food"]
    }
  }
}


