﻿{
  "format_version": "1.20.50",
  "minecraft:attachable": {
    "description": {
      "identifier": "ptd_lmhc:flavorboom_waffle",
      "materials": {
        "default": "entity_alphatest"
      },
      "textures": {
        "default": "textures/ptd/lm_hc/items/food/flavorboom_waffle",
        "enchanted": "textures/misc/enchanted_item_glint"
      },
      "geometry": {
        "default": "geometry.flavorboom_waffle"
      },
      "animations": {
        "hold_first_person": "animation.food.flavorboom_waffle.hold_first_person",
        "hold_third_person": "animation.food.flavorboom_waffle.hold_third_person",
        "eat": "animation.food.flavorboom_waffle.eat",
        "eat_controller": "controller.animation.food.eat",
        "general": "controller.animation.food.general"
      },
      "scripts": {
        "animate": ["general", "eat_controller"]
      },
      "render_controllers": ["controller.render.food"]
    }
  }
}


