{"format_version": "1.8.0", "animations": {"animation.golem_guardian.attack": {"animation_length": 0.5, "loop": false, "bones": {"arm0": {"rotation": {"0.0": [0, 0, 0], "0.05": [-45, 0, 0], "0.15": [-90, 0, 0], "0.2": [-114, 0, 0], "0.3": [-90, 0, 0], "0.4": [-45, 0, 0], "0.5": [0, 0, 0]}}, "arm1": {"rotation": {"0.0": [0, 0, 0], "0.05": [-45, 0, 0], "0.15": [-90, 0, 0], "0.2": [-114, 0, 0], "0.3": [-90, 0, 0], "0.4": [-45, 0, 0], "0.5": [0, 0, 0]}}}}, "animation.golem_guardian.move": {"loop": true, "bones": {"arm0": {"rotation": ["-variable.modified_tcos0 * 2.0", 0.0, 0.0]}, "arm1": {"rotation": ["variable.modified_tcos0 * 2.0", 0.0, 0.0]}}}, "animation.golem_guardian.move_to_target": {"loop": true, "bones": {"arm0": {"rotation": ["((math.abs(math.mod(query.modified_distance_moved, 13) - 6.5) - 3.25) / 2.25) * 30.0", 0.0, 0.0]}, "arm1": {"rotation": ["((math.abs(math.mod(query.modified_distance_moved, 13) - 6.5) - 3.25) / 2.25) * -30.0", 0.0, 0.0]}}}, "animation.golem_guardian.walk": {"loop": true, "bones": {"body": {"rotation": [0.0, 0.0, "variable.modified_tcos0 / 1.5"]}, "head": {"rotation": [0.0, 0.0, "variable.modified_tcos0 / 1.5"]}, "leg0": {"rotation": ["variable.modified_tcos0 * 6.0", 0.0, 0.0]}, "leg1": {"rotation": ["-variable.modified_tcos0 * 6.0", 0.0, 0.0]}}}, "animation.golem_guardian.walk_to_target": {"loop": true, "bones": {"body": {"rotation": [0.0, 0.0, "2.0 * (math.abs(math.mod(query.modified_distance_moved + 6, 13.0) - 6.5) - 3.25)"]}, "head": {"rotation": [0.0, 0.0, "2.0 * (math.abs(math.mod(query.modified_distance_moved + 6, 13.0) - 6.5) - 3.25)"]}, "leg0": {"rotation": ["(math.cos(query.modified_distance_moved * 38.17) * 40.0)", 0.0, 0.0]}, "leg1": {"rotation": ["(math.cos(query.modified_distance_moved * 38.17 + 180) * 40.0)", 0.0, 0.0]}}}}}