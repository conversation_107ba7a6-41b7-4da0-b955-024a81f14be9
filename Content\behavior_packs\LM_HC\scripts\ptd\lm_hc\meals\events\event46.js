import { getRandomLocation } from '../../utilities/vector3';
import { spawnEntitiesWithInterval } from '../../utilities/summonEntity';
// Constants for bee spawning
const SPAWN_DELAY_TICKS = 2; // 0.1 seconds between spawns
const BEE_COUNT = 20; // Number of bees to spawn
/**
 * Event 46: Swarm of Bees
 * Spawns 20 angry bees that immediately target the player
 * @param player The player who triggered the event
 */
export function event46(player) {
    try {
        // Configure the entity type and quantity to spawn
        const entityConfigs = [{ entityId: 'ptd_lmhc:angry_bee', count: BEE_COUNT }];
        // Create a location callback function that returns a random position around the player
        const getSpawnLocation = () => {
            const spawnPos = getRandomLocation(player.location, player.dimension, 3, // minDistance
            4, // additionalOffset (maxDistance = 7)
            getRandomHeight(), // random height above player
            true);
            // Play particle effect at spawn location if position is valid
            if (spawnPos) {
                player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
                player.dimension.playSound('mob.endermen.loop', spawnPos);
            }
            return spawnPos;
        };
        // Get a random height offset for more natural bee spawning
        function getRandomHeight() {
            return Math.random() * 4 + 1; // Between 1 and 5 blocks above
        }
        // Define a callback to be executed when a bee is spawned
        const onBeeSpawned = (bee) => {
            try {
                // The bee is already angry by default due to entity definition
                if (bee && bee) {
                    // Play a bee sound when it spawns
                    player.dimension.playSound('mob.bee.hurt', bee.location, {
                        volume: 0.5,
                        pitch: Math.random() * 0.4 + 0.8
                    });
                }
            }
            catch (error) {
                console.warn(`Failed to process bee spawn: ${error}`);
            }
        };
        // Start spawning angry bees with the specified delay between each
        spawnEntitiesWithInterval(player.dimension, entityConfigs, getSpawnLocation, SPAWN_DELAY_TICKS, onBeeSpawned).catch((error) => {
            console.warn(`Error in event46 spawning process: ${error}`);
        });
    }
    catch (error) {
        console.warn(`Failed to execute event 46: ${error}`);
    }
    return;
}
