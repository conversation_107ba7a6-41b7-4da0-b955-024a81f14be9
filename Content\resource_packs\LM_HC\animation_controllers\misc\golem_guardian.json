{"format_version": "1.10.0", "animation_controllers": {"controller.animation.golem_guardian.arm_movement": {"initial_state": "default", "states": {"attack": {"animations": ["attack"], "transitions": [{"default": "!query.has_target && variable.attack_time <= 0.0 && query.all_animations_finished"}, {"has_target": "query.has_target && variable.attack_time <= 0.0 && query.all_animations_finished"}]}, "default": {"animations": ["move"], "transitions": [{"attack": "variable.attack_time > 0.0"}, {"has_target": "query.has_target"}]}, "has_target": {"animations": ["move_to_target"], "transitions": [{"attack": "variable.attack_time > 0.0"}, {"default": "!query.has_target"}]}}}}}