{"format_version": "1.10.0", "particle_effect": {"description": {"identifier": "ptd_lmhc:land_01", "basic_render_parameters": {"material": "particles_add", "texture": "textures/ptd/lm_hc/particles/circle_01"}}, "curves": {"variable.grav": {"type": "catmull_rom", "input": "v.particle_age", "horizontal_range": "v.particle_lifetime", "nodes": [1, 1.34, 0.59, 0.47, 0]}, "variable.size": {"type": "catmull_rom", "input": "v.particle_age", "horizontal_range": "v.particle_lifetime", "nodes": [0, 0.25, 1, 1]}}, "components": {"minecraft:emitter_rate_instant": {"num_particles": 4}, "minecraft:emitter_lifetime_once": {"active_time": 1}, "minecraft:emitter_shape_point": {"offset": [0, 0.05, 0]}, "minecraft:particle_lifetime_expression": {"max_lifetime": 0.35}, "minecraft:particle_initial_speed": 0, "minecraft:particle_motion_dynamic": {}, "minecraft:particle_appearance_billboard": {"size": ["variable.size*1.5", "variable.size*1.5"], "facing_camera_mode": "emitter_transform_xz", "uv": {"texture_width": 32, "texture_height": 32, "uv": [0, 0], "uv_size": [32, 32]}}, "minecraft:particle_appearance_tinting": {"color": {"interpolant": "v.particle_age / v.particle_lifetime", "gradient": {"0.0": "#00000000", "0.51": "#302C2C2C", "1.0": "#00000000"}}}}}}