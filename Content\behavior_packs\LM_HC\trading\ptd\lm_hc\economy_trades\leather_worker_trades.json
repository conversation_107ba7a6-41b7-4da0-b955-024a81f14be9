{"tiers": [{"total_exp_required": 0, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:leather", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 2}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:leather_leggings", "quantity": 1, "functions": [{"function": "random_dye"}, {"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 1, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 2, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:leather_chestplate", "quantity": 1, "functions": [{"function": "random_dye"}, {"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 1, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 10, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:flint", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 2}], "trader_exp": 10, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:leather_helmet", "quantity": 1, "functions": [{"function": "random_dye"}, {"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:leather_boots", "quantity": 1, "functions": [{"function": "random_dye"}, {"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 70, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:rabbit_hide", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 3}], "trader_exp": 20, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 2, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:leather_chestplate", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 25, "max": 39}}]}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 2, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:saddle", "quantity": 1}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 150, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:scute", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 4}], "trader_exp": 30, "max_uses": 12, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 2, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:leather_horse_armor", "quantity": 1, "functions": [{"function": "random_dye"}]}], "trader_exp": 15, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 3, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:leather_chestplate", "quantity": 1, "functions": [{"function": "random_dye"}, {"function": "enchant_with_levels", "levels": {"min": 30, "max": 39}}]}], "trader_exp": 15, "max_uses": 12, "reward_exp": true}]}]}]}