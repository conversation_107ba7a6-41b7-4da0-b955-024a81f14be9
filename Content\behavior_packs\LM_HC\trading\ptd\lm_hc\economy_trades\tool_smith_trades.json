{"tiers": [{"total_exp_required": 0, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:coal:0", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 5}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:diamond_axe", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 1, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:diamond_shovel", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 1, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 10, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:iron_ingot", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 4}], "trader_exp": 10, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:diamond_pickaxe", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:diamond_hoe", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 70, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:flint", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 4}], "trader_exp": 20, "max_uses": 12, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 2, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:netherite_pickaxe", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 25, "max": 30}}]}], "trader_exp": 10, "max_uses": 3, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 2, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:netherite_axe", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 25, "max": 30}}]}], "trader_exp": 10, "max_uses": 3, "reward_exp": true}]}]}, {"total_exp_required": 150, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:diamond", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 8}], "trader_exp": 30, "max_uses": 12, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 3, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:netherite_shovel", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 25, "max": 30}}]}], "trader_exp": 15, "max_uses": 3, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 3, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:netherite_hoe", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 25, "max": 30}}]}], "trader_exp": 15, "max_uses": 3, "reward_exp": true}]}]}, {"total_exp_required": 250, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 5, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:netherite_ingot", "quantity": 1}], "trader_exp": 30, "max_uses": 3, "reward_exp": true}]}]}]}