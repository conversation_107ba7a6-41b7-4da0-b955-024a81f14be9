import { ItemStack, system } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { getRandomInt } from '../../utilities/rng';
/**
 * Event 31: Emerald Rain
 * Makes 30-50 emeralds rain from the sky around the player over time
 * @param player The player who triggered the event
 */
export async function event31(player) {
    try {
        // Determine number of emeralds to spawn (30-50)
        const emeraldCount = getRandomInt(30, 50);
        const emeraldItem = new ItemStack('minecraft:emerald', 1);
        let emeraldsSpawned = 0;
        // Create an interval to spawn emeralds over time
        const rainInterval = system.runInterval(() => {
            try {
                // Get spawn location above player
                const spawnLocation = getRandomLocation(player.getHeadLocation(), player.dimension, 0, // Base offset in xyz axes
                getRandomInt(-4, 4), // Additional random XZ offset
                getRandomInt(4, 12), // Random y offset
                true);
                if (spawnLocation) {
                    // Spawn emerald item
                    player.dimension.spawnItem(emeraldItem, spawnLocation);
                    // Add particle effects for visual feedback
                    player.dimension.spawnParticle('minecraft:crop_growth_emitter', spawnLocation);
                    player.dimension.playSound('random.pop', spawnLocation);
                    emeraldsSpawned++;
                    // Stop the interval when all emeralds have been spawned
                    if (emeraldsSpawned >= emeraldCount) {
                        system.clearRun(rainInterval);
                    }
                }
            }
            catch (error) {
                console.warn(`Failed to spawn emerald in interval: ${error}`);
                system.clearRun(rainInterval);
            }
        }, 3);
    }
    catch (error) {
        console.warn(`Failed to execute event 31 (Emerald Rain): ${error}`);
    }
}
