{"format_version": "1.19.0", "animation_controllers": {"controller.animation.food.general": {"initial_state": "first_person", "states": {"first_person": {"animations": ["hold_first_person"], "transitions": [{"third_person": "!c.is_first_person"}]}, "third_person": {"animations": ["hold_third_person"], "transitions": [{"first_person": "c.is_first_person"}]}}}, "controller.animation.food.eat": {"states": {"default": {"transitions": [{"eat": "c.is_first_person && q.is_using_item"}]}, "eat": {"animations": ["eat"], "transitions": [{"default": "!q.is_using_item || q.all_animations_finished || !c.is_first_person"}]}}}}}