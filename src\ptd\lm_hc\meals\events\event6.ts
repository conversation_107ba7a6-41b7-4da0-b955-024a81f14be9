import { Enti<PERSON>, Player } from '@minecraft/server';
import { onEatMeal } from '../events';
import { getRandomLocation } from '../../utilities/vector3';
import { EntityQuantityConfig, spawnEntitiesWithInterval } from '../../utilities/summonEntity';

// Constants for creeper spawning
const SPAWN_DELAY_TICKS: number = 5; // 0.25 seconds between spawns

/**
 * Event 6. Supercharged Creeper: Sequentially spawns 5 charged creepers around the player
 *
 * Spawning Logic:
 * 1. Attempts to find safe spawn locations around the player using utility function
 * 2. Spawns creepers sequentially with particle effects at valid locations
 * 3. Waits for SPAWN_DELAY_TICKS between each spawn
 * 4. Charges each creeper immediately after spawning
 * 5. Ensures minimum safe distance from player
 *
 * @param player - The player object who triggered the event
 * @returns {void} - Function exits after initiating sequential creeper spawning
 */
export function event6(player: Player): void {
  try {
    // Configure the entity type and quantity to spawn
    const entityConfigs: EntityQuantityConfig[] = [{ entityId: 'minecraft:creeper', count: 5 }];

    // Create a location callback function that returns a random position around the player
    const getSpawnLocation = () => {
      const spawnPos = getRandomLocation(
        player.location,
        player.dimension,
        9, // minDistance
        4, // additionalOffset (maxDistance = 8)
        0,
        true
      );

      // Play particle effect at spawn location if position is valid
      if (spawnPos) {
        player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
        player.dimension.playSound('mob.endermen.portal', spawnPos);
        player.dimension.playSound('random.fizz', spawnPos);
      }

      return spawnPos;
    };

    // Define a callback to be executed when a creeper is spawned to charge it
    const onCreeperSpawned = (creeper: Entity) => {
      try {
        // Charge the creeper immediately after spawning
        if (creeper && creeper) {
          creeper.triggerEvent('minecraft:become_charged');
        }
      } catch (error) {
        console.warn(`Failed to charge creeper: ${error}`);
      }
    };

    // Start spawning charged creepers with the specified delay between each
    spawnEntitiesWithInterval(
      player.dimension,
      entityConfigs,
      getSpawnLocation,
      SPAWN_DELAY_TICKS,
      onCreeperSpawned
    ).catch((error) => {
      console.warn(`Error in event6 spawning process: ${error}`);
      onEatMeal(player); // Retry on error
    });
  } catch (error) {
    console.warn(`Failed to execute event 6: ${error}`);
    onEatMeal(player); // Retry on error
  }
  return;
}
