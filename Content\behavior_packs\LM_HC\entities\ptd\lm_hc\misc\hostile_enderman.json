{
  "format_version": "1.21.60",
  "minecraft:entity": {
    "description": {
      "identifier": "ptd_lmhc:hostile_enderman",
      "spawn_category": "monster",
      "is_spawnable": true,
      "is_summonable": true
    },

    "component_groups": {
      "minecraft:enderman_calm": {
        "minecraft:on_target_acquired": {
          "event": "minecraft:become_angry",
          "target": "self"
        },
        "minecraft:movement": {
          "value": 0.3
        }
      },
      "minecraft:enderman_angry": {
        "minecraft:angry": {
          "duration": 25,
          "calm_event": {
            "event": "minecraft:on_calm",
            "target": "self"
          }
        },
        "minecraft:movement": {
          "value": 0.45
        },
        "minecraft:behavior.melee_box_attack": {
          "priority": 2
        }
      },
      "minecraft:riding": {
        "minecraft:environment_sensor": {
          "triggers": {
            "filters": {
              "test": "is_riding",
              "subject": "self",
              "operator": "==",
              "value": false
            },
            "event": "minecraft:stopped_riding"
          }
        }
      },
      "minecraft:not_riding": {
        "minecraft:environment_sensor": {
          "triggers": {
            "filters": {
              "test": "is_riding",
              "subject": "self",
              "operator": "==",
              "value": true
            },
            "event": "minecraft:started_riding"
          }
        },
        "minecraft:teleport": {
          "random_teleports": true,
          "max_random_teleport_time": 30,
          "random_teleport_cube": [32, 32, 32],
          "target_distance": 16,
          "target_teleport_chance": 0.05,
          "light_teleport_chance": 0.05
        }
      }
    },

    "components": {
      "minecraft:is_hidden_when_invisible": {},
      "minecraft:renders_when_invisible": {},
      "minecraft:experience_reward": {
        "on_death": "query.last_hit_by_player ? 5 : 0"
      },
      "minecraft:type_family": {
        "family": ["enderman", "monster", "mob"]
      },
      "minecraft:breathable": {
        "total_supply": 15,
        "suffocate_time": 0
      },
      "minecraft:nameable": {},
      "minecraft:loot": {
        "table": "loot_tables/entities/enderman.json"
      },
      "minecraft:health": {
        "value": 40,
        "max": 40
      },
      "minecraft:hurt_on_condition": {
        "damage_conditions": [
          {
            "filters": { "test": "in_lava", "subject": "self" },
            "cause": "lava",
            "damage_per_tick": 4
          },
          {
            "filters": { "test": "in_contact_with_water" },
            "cause": "drowning",
            "damage_per_tick": 1
          }
        ]
      },
      "minecraft:attack": {
        "damage": 7
      },
      "minecraft:follow_range": {
        "value": 64,
        "max": 64
      },
      "minecraft:collision_box": {
        "width": 0.6,
        "height": 2.9
      },
      "minecraft:teleport": {
        "random_teleports": true,
        "max_random_teleport_time": 30,
        "random_teleport_cube": [32, 32, 32],
        "target_distance": 16,
        "target_teleport_chance": 0.05,
        "light_teleport_chance": 0.05
      },
      "minecraft:looked_at": {
        "search_radius": 64.0,
        "set_target": "once_and_stop_scanning",
        "find_players_only": true,
        "min_looked_at_duration": 0.25,
        "filters": {
          "test": "has_equipment",
          "domain": "head",
          "subject": "other",
          "operator": "not",
          "value": "carved_pumpkin"
        }
      },
      "minecraft:despawn": {
        "despawn_from_distance": {}
      },
      "minecraft:behavior.nearest_attackable_target": {
        "priority": 1,
        "must_see": false,
        "attack_interval": 1,
        "entity_types": [
          {
            "filters": {
              "test": "is_family",
              "subject": "other",
              "value": "endermite"
            },
            "max_dist": 64
          },
          {
            "filters": {
              "test": "is_family",
              "subject": "other",
              "value": "player"
            },
            "max_dist": 64
          }
        ]
      },
      "minecraft:navigation.walk": {
        "can_path_over_water": false,
        "avoid_water": true
      },
      "minecraft:movement.basic": {},
      "minecraft:jump.static": {},
      "minecraft:can_climb": {},
      "minecraft:physics": {},
      "minecraft:pushable": {
        "is_pushable": true,
        "is_pushable_by_piston": true
      },
      "minecraft:conditional_bandwidth_optimization": {
        "default_values": {
          "max_optimized_distance": 80.0,
          "max_dropped_ticks": 10,
          "use_motion_prediction_hints": true
        }
      },
      "minecraft:variable_max_auto_step": {
        "base_value": 1.0625, // 1 block + 1 pixel
        "jump_prevented_value": 0.5625 // 0.5 blocks + 1 pixel
      },
      "minecraft:behavior.float": {
        "priority": 0
      },
      "minecraft:behavior.random_stroll": {
        "priority": 7,
        "speed_multiplier": 1.0
      },
      "minecraft:behavior.look_at_player": {
        "priority": 8,
        "look_distance": 8.0,
        "probability": 8.0
      },
      "minecraft:behavior.random_look_around": {
        "priority": 8
      },
      "minecraft:behavior.hurt_by_target": {
        "priority": 3
      }
    },

    "events": {
      "minecraft:entity_spawned": {
        "remove": {},
        "add": {
          "component_groups": ["minecraft:enderman_calm", "minecraft:not_riding"]
        }
      },
      "minecraft:become_angry": {
        "remove": {
          "component_groups": ["minecraft:enderman_calm"]
        },
        "add": {
          "component_groups": ["minecraft:enderman_angry"]
        }
      },
      "minecraft:on_calm": {
        "remove": {
          "component_groups": ["minecraft:enderman_angry"]
        },
        "add": {
          "component_groups": ["minecraft:enderman_calm"]
        }
      },
      "minecraft:stopped_riding": {
        "add": {
          "component_groups": ["minecraft:not_riding"]
        },
        "remove": {
          "component_groups": ["minecraft:riding"]
        }
      },
      "minecraft:started_riding": {
        "add": {
          "component_groups": ["minecraft:riding"]
        },
        "remove": {
          "component_groups": ["minecraft:not_riding"]
        }
      }
    }
  }
}
