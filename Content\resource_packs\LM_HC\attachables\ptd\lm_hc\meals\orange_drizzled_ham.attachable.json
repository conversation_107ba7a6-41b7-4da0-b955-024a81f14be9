﻿{
  "format_version": "1.20.50",
  "minecraft:attachable": {
    "description": {
      "identifier": "ptd_lmhc:orange_drizzled_ham",
      "materials": {
        "default": "entity_alphatest"
      },
      "textures": {
        "default": "textures/ptd/lm_hc/items/food/orange_drizzled_ham",
        "enchanted": "textures/misc/enchanted_item_glint"
      },
      "geometry": {
        "default": "geometry.orange_drizzled_ham"
      },
      "animations": {
        "hold_first_person": "animation.food.orange_drizzled_ham.hold_first_person",
        "hold_third_person": "animation.food.orange_drizzled_ham.hold_third_person",
        "eat": "animation.food.orange_drizzled_ham.eat",
        "eat_controller": "controller.animation.food.eat",
        "general": "controller.animation.food.general"
      },
      "scripts": {
        "animate": [
          "general",
          "eat_controller"
        ]
      },
      "render_controllers": [
        "controller.render.food"
      ]
    }
  }
}