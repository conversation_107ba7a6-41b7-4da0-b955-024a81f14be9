{"format_version": "1.21.20", "minecraft:entity": {"description": {"identifier": "ptd_lmhc:mega_ravager", "is_spawnable": true, "is_summonable": true}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:type_family": {"family": ["ravager", "monster", "mob"]}, "minecraft:nameable": {}, "minecraft:collision_box": {"width": 1.95, "height": 2.2}, "minecraft:scale": {"value": 1.5}, "minecraft:loot": {"table": "loot_tables/entities/ravager.json"}, "minecraft:health": {"value": 150, "max": 150}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.3}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:attack": {"damage": {"range_min": 8, "range_max": 16}}, "minecraft:damage_sensor": {"triggers": {"cause": "fall", "deals_damage": false}}, "minecraft:knockback_resistance": {"value": 0.9}, "minecraft:ravager_blocked": {"knockback_strength": 5.0, "reaction_choices": [{"weight": 1, "value": {"event": "minecraft:become_stunned", "target": "self"}}, {"weight": 1}]}, "minecraft:break_blocks": {"breakable_blocks": ["bamboo", "bamboo_sapling", "beetroot", "brown_mushroom", "carrots", "carved_pumpkin", "chorus_flower", "chorus_plant", "crimson_fungus", "double_plant", "leaves", "leaves2", "lit_pumpkin", "melon_block", "melon_stem", "potatoes", "pumpkin", "pumpkin_stem", "red_flower", "red_mushroom", "reeds", "sapling", "sweet_berry_bush", "tallgrass", "turtle_egg", "vine", "warped_fungus", "waterlily", "wheat", "yellow_flower"]}, "minecraft:behavior.melee_attack": {"priority": 4, "track_target": true, "speed_multiplier": 1.2}, "minecraft:behavior.random_stroll": {"priority": 5, "speed_multiplier": 1.0}, "minecraft:behavior.random_look_around": {"priority": 6}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_see": true, "reselect_targets": true, "within_radius": 25.0, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}]}, "max_dist": 25}]}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {}}, "component_groups": {"minecraft:stunned": {"minecraft:timer": {"time": 2, "looping": false, "time_down_event": {"event": "minecraft:end_stun", "target": "self"}}, "minecraft:is_stunned": {}}}, "events": {"minecraft:entity_spawned": {}, "minecraft:become_stunned": {"add": {"component_groups": ["minecraft:stunned"]}}, "minecraft:end_stun": {"remove": {"component_groups": ["minecraft:stunned"]}}}}}