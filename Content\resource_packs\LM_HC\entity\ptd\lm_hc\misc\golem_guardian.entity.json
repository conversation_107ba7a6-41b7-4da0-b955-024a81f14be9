{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_lmhc:golem_guardian", "materials": {"default": "iron_golem"}, "textures": {"default": "textures/entity/iron_golem", "cracked_high": "textures/entity/iron_golem/cracked_high", "cracked_med": "textures/entity/iron_golem/cracked_medium", "cracked_low": "textures/entity/iron_golem/cracked_low", "cracked_none": "textures/entity/iron_golem/cracked_none"}, "geometry": {"default": "geometry.irongolem"}, "animations": {"walk": "animation.golem_guardian.walk", "move": "animation.golem_guardian.move", "attack": "animation.golem_guardian.attack", "look_at_target": "animation.common.look_at_target", "walk_to_target": "animation.golem_guardian.walk_to_target", "move_to_target": "animation.golem_guardian.move_to_target", "move_controller": "controller.animation.iron_golem.move", "arm_controller": "controller.animation.golem_guardian.arm_movement"}, "render_controllers": ["controller.render.iron_golem", "controller.render.iron_golem_cracks"], "scripts": {"pre_animation": ["variable.modified_tcos0 = Math.clamp(((Math.cos(query.modified_distance_moved * 13.5) * Math.min(query.modified_move_speed, 0.6) / variable.gliding_speed_value) * 25.0), -12.5, 12.5);"], "animate": ["look_at_target", "move_controller", "arm_controller"]}, "spawn_egg": {"base_color": "#dbcdc2", "overlay_color": "#74a332"}}}}