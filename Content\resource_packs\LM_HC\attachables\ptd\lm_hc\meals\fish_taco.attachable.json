﻿{
  "format_version": "1.20.50",
  "minecraft:attachable": {
    "description": {
      "identifier": "ptd_lmhc:fish_taco",
      "materials": {
        "default": "entity_alphatest"
      },
      "textures": {
        "default": "textures/ptd/lm_hc/items/food/fish_taco",
        "enchanted": "textures/misc/enchanted_item_glint"
      },
      "geometry": {
        "default": "geometry.fish_taco"
      },
      "animations": {
        "hold_first_person": "animation.food.fish_taco.hold_first_person",
        "hold_third_person": "animation.food.fish_taco.hold_third_person",
        "eat": "animation.food.fish_taco.eat",
        "eat_controller": "controller.animation.food.eat",
        "general": "controller.animation.food.general"
      },
      "scripts": {
        "animate": ["general", "eat_controller"]
      },
      "render_controllers": ["controller.render.food"]
    }
  }
}


