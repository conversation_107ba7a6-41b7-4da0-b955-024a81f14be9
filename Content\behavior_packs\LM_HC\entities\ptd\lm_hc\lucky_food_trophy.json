{"format_version": "1.21.70", "minecraft:entity": {"description": {"identifier": "ptd_lmhc:lucky_food_trophy", "is_spawnable": false, "is_summonable": true, "is_experimental": false, "properties": {"ptd_lmhc:times_to_destroy": {"client_sync": true, "type": "int", "default": 0, "range": [0, 7]}}, "animations": {"idle_sounds": "animation.lucky_food_trophy.idle_sounds"}, "scripts": {"animate": ["idle_sounds"]}}, "events": {"ptd_lmhc:increment_destroy_times": {"sequence": [{"add": {"component_groups": ["ptd_lmhc:reset_destroy_times"]}}, {"set_property": {"ptd_lmhc:times_to_destroy": "math.min(q.property('ptd_lmhc:times_to_destroy') + 1, 7)"}}]}, "ptd_lmhc:reset_destroy_times": {"sequence": [{"set_property": {"ptd_lmhc:times_to_destroy": 0}}]}}, "component_groups": {"ptd_lmhc:reset_destroy_times": {"minecraft:timer": {"time": 0.8, "time_down_event": {"event": "ptd_lmhc:reset_destroy_times"}}}}, "components": {"minecraft:type_family": {"family": ["trophy", "inanimate"]}, "minecraft:health": {"value": 1, "max": 1}, "minecraft:is_collidable": {}, "minecraft:physics": {"has_collision": true, "has_gravity": true}, "minecraft:collision_box": {"width": 1.7, "height": 1}, "minecraft:custom_hit_test": {"hitboxes": [{"width": 1.7, "height": 2.2, "pivot": [0, 1.1, 0]}]}, "minecraft:knockback_resistance": {"value": 1.0}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:nameable": {"always_show": true, "allow_name_tag_renaming": true}, "minecraft:persistent": {}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no", "on_damage": {"event": "ptd_lmhc:increment_destroy_times"}}]}, "minecraft:conditional_bandwidth_optimization": {}}}}