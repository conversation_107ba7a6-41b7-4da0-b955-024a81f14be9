import { system } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { getRandomInt } from '../../utilities/rng';
/**
 * Event 42: Mega Ravager
 * Summons a Ravager with increased speed and attack power near the player
 *
 * @param player - The player to spawn the ravager near
 */
export async function event42(player) {
    try {
        // Get a valid spawn location near the player
        const location = getRandomLocation(player.location, player.dimension, 3, // base offset (increased to give more space for the larger ravager)
        getRandomInt(-3, 3), // additional horizontal offset
        0, // Y offset (spawn at ground level)
        true // check for air block
        );
        if (location) {
            // Spawn the mega ravager and give it a custom name
            const ravager = player.dimension.spawnEntity('ptd_lmhc:mega_ravager', location);
            ravager.nameTag = '§6Mega Ravager§r';
            player.dimension.spawnParticle('minecraft:large_explosion', location);
            await system.waitTicks(1);
            // Play spawn effects
            player.dimension.spawnParticle('minecraft:knockback_roar_particle', location);
        }
    }
    catch (error) {
        console.warn(`Failed to execute event 42: ${error}`);
    }
}
