{"tiers": [{"trades": [{"wants": [{"item": "minecraft:coal:0", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 4}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:diamond_shovel", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:iron_ingot", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 3}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:diamond_pickaxe", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:flint", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 2}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:diamond_axe", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:diamond", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 5}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:diamond_hoe", "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:netherite_scrap", "quantity": 1}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:netherite_ingot", "quantity": 1}]}]}]}