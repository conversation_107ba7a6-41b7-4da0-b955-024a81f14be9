{"tiers": [{"total_exp_required": 0, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:rotten_flesh", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 3}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:redstone", "quantity": 64}], "trader_exp": 1, "max_uses": 16, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:lapis_lazuli", "quantity": 32}], "trader_exp": 1, "max_uses": 16, "reward_exp": true}]}]}, {"total_exp_required": 10, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:gold_ingot", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 5}], "trader_exp": 10, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:glowstone", "quantity": 16}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:ender_pearl", "quantity": 8}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 70, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:rabbit_foot", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 6}], "trader_exp": 20, "max_uses": 12, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:experience_bottle", "quantity": 16}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:potion", "quantity": 3, "functions": [{"function": "set_data", "data": 8}]}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 150, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:scute", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 7}], "trader_exp": 30, "max_uses": 12, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 5, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:dragon_breath", "quantity": 8}], "trader_exp": 15, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 5, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:nether_star", "quantity": 1}], "trader_exp": 15, "max_uses": 3, "reward_exp": true}]}]}]}