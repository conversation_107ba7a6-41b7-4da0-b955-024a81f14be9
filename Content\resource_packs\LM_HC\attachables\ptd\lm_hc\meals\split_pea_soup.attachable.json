﻿{
  "format_version": "1.20.50",
  "minecraft:attachable": {
    "description": {
      "identifier": "ptd_lmhc:split_pea_soup",
      "materials": {
        "default": "entity_alphatest"
      },
      "textures": {
        "default": "textures/ptd/lm_hc/items/food/split_pea_soup",
        "enchanted": "textures/misc/enchanted_item_glint"
      },
      "geometry": {
        "default": "geometry.split_pea_soup"
      },
      "animations": {
        "hold_first_person": "animation.food.split_pea_soup.hold_first_person",
        "hold_third_person": "animation.food.split_pea_soup.hold_third_person",
        "eat": "animation.food.split_pea_soup.eat",
        "eat_controller": "controller.animation.food.eat",
        "general": "controller.animation.food.general"
      },
      "scripts": {
        "animate": [
          "general",
          "eat_controller"
        ]
      },
      "render_controllers": [
        "controller.render.food"
      ]
    }
  }
}