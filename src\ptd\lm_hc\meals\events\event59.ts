import { Player, system, Entity } from '@minecraft/server';

/**
 * Event 59: Mob Smite
 * Kills nearby hostile mobs instantly with lightning strikes
 * Kills 5 mobs per interval for 5 intervals (25 mobs total)
 *
 * @param player - The player who triggered the event
 */
export function event59(player: Player): void {
  try {
    // Function to kill mobs in an interval
    let intervals = 0;
    const smiteMobs = system.runInterval(() => {
      try {
        const location = player.location;
        // Get nearby entities (mostly hostile mobs after filtering)
        const nearbyEntities: Entity[] = player.dimension.getEntities({
          location: location,
          closest: 1,
          families: ['monster'],
          maxDistance: 64
        });

        // Kill each mob with lightning
        for (const target of nearbyEntities) {
          try {
            // Make sure entity is still valid
            if (!target) continue;

            // Strike lightning at the mob's position
            player.dimension.spawnEntity('minecraft:lightning_bolt', target.location);

            // Play effects
            player.dimension.spawnParticle('minecraft:large_explosion', target.location);

            // Kill the mob
            target.kill();
          } catch (targetError) {
            console.warn(`Failed to smite a target: ${targetError}`);
          }
        }

        // Increment interval counter
        intervals++;

        // Stop after 5 intervals
        if (intervals >= 10) {
          system.clearRun(smiteMobs);

          // Final effect to indicate end of smiting
          player.dimension.spawnParticle('minecraft:totem_particle', {
            x: player.location.x,
            y: player.location.y + 1,
            z: player.location.z
          });
          player.playSound('random.totem', { volume: 1.0 });
        }
      } catch (intervalError) {
        console.warn(`Error in smite interval: ${intervalError}`);
        system.clearRun(smiteMobs);
      }
    }, 40); // 40 ticks = 2 seconds between intervals
  } catch (error) {
    console.warn(`Failed to execute event 59: ${error}`);
  }
  return;
}
