{"format_version": "1.21.20", "minecraft:entity": {"description": {"identifier": "ptd_lmhc:golem_guardian", "is_spawnable": true, "is_summonable": true}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:type_family": {"family": ["irongolem", "mob"]}, "minecraft:nameable": {}, "minecraft:collision_box": {"width": 1.4, "height": 2.9}, "minecraft:scale": {"value": 2}, "minecraft:loot": {"table": "loot_tables/entities/iron_golem.json"}, "minecraft:health": {"value": 150, "max": 150}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.25}, "minecraft:navigation.walk": {"can_path_over_water": false, "avoid_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "iron_ingot"}, {"test": "is_missing_health", "value": true}]}}, "use_item": true, "health_amount": 25, "play_sounds": "irongolem.repair", "interact_text": "action.interact.repair"}]}, "minecraft:attack": {"damage": {"range_min": 14, "range_max": 42}}, "minecraft:damage_sensor": {"triggers": {"cause": "fall", "deals_damage": false}}, "minecraft:knockback_resistance": {"value": 1.0}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0}, "minecraft:balloonable": {"mass": 2.0}, "minecraft:preferred_path": {"max_fall_blocks": 1, "jump_cost": 5, "default_block_cost": 1.5, "preferred_path_blocks": [{"cost": 0, "blocks": ["grass_path"]}, {"cost": 1, "blocks": ["cobblestone", "stone", "granite", "polished_granite", "diorite", "polished_diorite", "andesite", "polished_andesite", "stone_bricks", "mossy_stone_bricks", "cracked_stone_bricks", "chiseled_stone_bricks", "sandstone", "cut_sandstone", "chiseled_sandstone", "smooth_sandstone", "mossy_cobblestone", "smooth_stone_slab", "sandstone_slab", "cobblestone_slab", "brick_slab", "stone_brick_slab", "quartz_slab", "nether_brick_slab", "red_sandstone_slab", "purpur_slab", "prismarine_slab", "dark_prismarine_slab", "prismarine_brick_slab", "mossy_cobblestone_slab", "smooth_sandstone_slab", "red_nether_brick_slab", "end_stone_brick_slab", "smooth_red_sandstone_slab", "polished_andesite_slab", "andesite_slab", "diorite_slab", "polished_diorite_slab", "granite_slab", "polished_granite_slab", "mossy_stone_brick_slab", "smooth_quartz_slab", "normal_stone_slab", "cut_sandstone_slab", "cut_red_sandstone_slab", "smooth_stone_double_slab", "sandstone_double_slab", "cobblestone_double_slab", "brick_double_slab", "stone_brick_double_slab", "quartz_double_slab", "nether_brick_double_slab", "red_sandstone_double_slab", "purpur_double_slab", "prismarine_double_slab", "dark_prismarine_double_slab", "prismarine_brick_double_slab", "mossy_cobblestone_double_slab", "smooth_sandstone_double_slab", "red_nether_brick_double_slab", "end_stone_brick_double_slab", "smooth_red_sandstone_double_slab", "polished_andesite_double_slab", "andesite_double_slab", "diorite_double_slab", "polished_diorite_double_slab", "granite_double_slab", "polished_granite_double_slab", "mossy_stone_brick_double_slab", "smooth_quartz_double_slab", "normal_stone_double_slab", "cut_sandstone_double_slab", "cut_red_sandstone_double_slab", "oak_slab", "spruce_slab", "birch_slab", "jungle_slab", "acacia_slab", "dark_oak_slab", "oak_double_slab", "spruce_double_slab", "birch_double_slab", "jungle_double_slab", "acacia_double_slab", "dark_oak_double_slab", "oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "brick_block", "nether_brick", "red_nether_brick", "end_bricks", "red_sandstone", "cut_red_sandstone", "chiseled_red_sandstone", "smooth_red_sandstone", "white_stained_glass", "orange_stained_glass", "magenta_stained_glass", "light_blue_stained_glass", "yellow_stained_glass", "lime_stained_glass", "pink_stained_glass", "gray_stained_glass", "light_gray_stained_glass", "cyan_stained_glass", "purple_stained_glass", "blue_stained_glass", "brown_stained_glass", "green_stained_glass", "red_stained_glass", "black_stained_glass", "glass", "glowstone", "prismarine", "emerald_block", "diamond_block", "lapis_block", "gold_block", "redstone_block", "purple_glazed_terracotta", "white_glazed_terracotta", "orange_glazed_terracotta", "magenta_glazed_terracotta", "light_blue_glazed_terracotta", "yellow_glazed_terracotta", "lime_glazed_terracotta", "pink_glazed_terracotta", "gray_glazed_terracotta", "silver_glazed_terracotta", "cyan_glazed_terracotta", "blue_glazed_terracotta", "brown_glazed_terracotta", "green_glazed_terracotta", "red_glazed_terracotta", "black_glazed_terracotta"]}, {"cost": 50, "blocks": ["bed", "lectern", "composter", "grindstone", "blast_furnace", "smoker", "fletching_table", "cartography_table", "brewing_stand", "smithing_table", "cauldron", "barrel", "loom", "stonecutter_block"]}]}, "minecraft:behavior.target_when_pushed": {"priority": 1, "percent_chance": 5.0, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "monster"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "creeper"}]}}]}, "minecraft:behavior.hurt_by_target": {"priority": 2, "entity_types": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "operator": "!=", "value": "player"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "creeper"}]}}}, "minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true}, "minecraft:behavior.move_towards_target": {"priority": 2, "speed_multiplier": 0.9, "within_radius": 32}, "minecraft:behavior.move_through_village": {"priority": 3, "speed_multiplier": 0.6, "only_at_night": true}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.6, "xz_dist": 16}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 8}, "minecraft:behavior.nearest_attackable_target": {"priority": 3, "must_reach": true, "must_see": true, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "monster"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "creeper"}]}, "within_default": 10}, {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "hoglin"}, {"test": "is_difficulty", "operator": "!=", "value": "peaceful"}]}, "max_dist": 16}, {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "<PERSON>oglin"}, {"test": "is_difficulty", "operator": "!=", "value": "peaceful"}]}, "max_dist": 16}]}, "minecraft:persistent": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:follow_range": {"value": 64}, "minecraft:conditional_bandwidth_optimization": {}}, "events": {}}}