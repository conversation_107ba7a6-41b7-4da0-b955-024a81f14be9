import { EntityComponentTypes, EntityDamageCause, system } from '@minecraft/server';
/**
 * Event 30: Poisonous Feast
 * Reduces player health to half a heart (0.5 hearts) while respecting vanilla mechanics
 * @param player The player who triggered the event
 */
export function event30(player) {
    try {
        const healthComponent = player.getComponent(EntityComponentTypes.Health);
        // Get current health values
        const currentHealth = healthComponent.currentValue;
        const targetHealth = 1; // 0.5 hearts = 1 health point in Minecraft
        // Don't reduce health if already at or below target
        if (currentHealth <= targetHealth) {
            return;
        }
        // Calculate damage needed
        const damageNeeded = currentHealth - targetHealth;
        // Apply damage smoothly over short interval
        system.runTimeout(() => {
            try {
                player.applyDamage(damageNeeded, {
                    cause: EntityDamageCause.magic,
                    damagingEntity: player
                });
                // Visual feedback
                player.dimension.spawnParticle('minecraft:dragon_breath_trail', player.location);
            }
            catch (error) {
                console.warn(`Failed to apply damage in event 30: ${error}`);
            }
        }, 1);
    }
    catch (error) {
        console.warn(`Failed to execute event 30: ${error}`);
    }
}
