{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_lmhc:zombie", "min_engine_version": "1.8.0", "materials": {"default": "zombie"}, "textures": {"default": "textures/entity/zombie/zombie"}, "geometry": {"default": "geometry.zombie.v1.8"}, "animations": {"rise": "animation.lmhc_zombie.rise", "humanoid_big_head": "animation.humanoid.big_head", "look_at_target_default": "animation.humanoid.look_at_target.default", "look_at_target_gliding": "animation.humanoid.look_at_target.gliding", "look_at_target_swimming": "animation.humanoid.look_at_target.swimming", "move": "animation.humanoid.move", "holding": "animation.humanoid.holding", "brandish_spear": "animation.humanoid.brandish_spear", "charging": "animation.humanoid.charging", "attack.rotations": "animation.humanoid.attack.rotations", "sneaking": "animation.humanoid.sneaking", "bob": "animation.humanoid.bob", "damage_nearby_mobs": "animation.humanoid.damage_nearby_mobs", "bow_and_arrow": "animation.humanoid.bow_and_arrow", "use_item_progress": "animation.humanoid.use_item_progress", "zombie_attack_bare_hand": "animation.zombie.attack_bare_hand", "swimming": "animation.zombie.swimming", "humanoid_baby_big_head": "controller.animation.humanoid.baby_big_head", "look_at_target": "controller.animation.humanoid.look_at_target", "move_controller": "controller.animation.humanoid.move", "holding_controller": "controller.animation.humanoid.holding", "charging_controller": "controller.animation.humanoid.charging", "attack": "controller.animation.humanoid.attack", "bob_controller": "controller.animation.humanoid.bob", "zombie_attack_bare_hand_controller": "controller.animation.zombie.attack_bare_hand", "swimming_controller": "controller.animation.zombie.swimming"}, "scripts": {"pre_animation": ["variable.tcos0 = (Math.cos(query.modified_distance_moved * 38.17) * query.modified_move_speed / variable.gliding_speed_value) * 57.3;"], "animate": ["rise", "humanoid_baby_big_head", "look_at_target", "move_controller", "holding_controller", "charging_controller", "attack", "bob_controller", "zombie_attack_bare_hand_controller", "swimming_controller"]}, "spawn_egg": {"texture": "spawn_egg", "texture_index": 12}, "render_controllers": ["controller.render.zombie"], "enable_attachables": true}}}