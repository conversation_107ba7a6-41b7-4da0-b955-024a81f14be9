import { Player } from '@minecraft/server';

/**
 * Event 45: Super Elytra
 * Gives the player an elytra with max enchantments
 * @param player The player who triggered the event
 */
export function event45(player: Player): void {
  try {
    // Give elytra to player by spawning it at their location
    player.runCommand('loot spawn ~~~ loot "ptd/lm_hc/event45"');

    // Play effects
    player.dimension.playSound('random.levelup', player.location);
    player.dimension.spawnParticle('minecraft:totem_particle', player.location);
  } catch (error) {
    console.warn(`Failed to execute event 45: ${error}`);
  }
  return;
}
