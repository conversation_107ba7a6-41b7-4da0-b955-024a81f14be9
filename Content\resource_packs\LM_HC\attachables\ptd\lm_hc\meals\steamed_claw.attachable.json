﻿{
  "format_version": "1.20.50",
  "minecraft:attachable": {
    "description": {
      "identifier": "ptd_lmhc:steamed_claw",
      "materials": {
        "default": "entity_alphatest"
      },
      "textures": {
        "default": "textures/ptd/lm_hc/items/food/steamed_claw",
        "enchanted": "textures/misc/enchanted_item_glint"
      },
      "geometry": {
        "default": "geometry.steamed_claw"
      },
      "animations": {
        "hold_first_person": "animation.food.steamed_claw.hold_first_person",
        "hold_third_person": "animation.food.steamed_claw.hold_third_person",
        "eat": "animation.food.steamed_claw.eat",
        "eat_controller": "controller.animation.food.eat",
        "general": "controller.animation.food.general"
      },
      "scripts": {
        "animate": ["general", "eat_controller"]
      },
      "render_controllers": ["controller.render.food"]
    }
  }
}


