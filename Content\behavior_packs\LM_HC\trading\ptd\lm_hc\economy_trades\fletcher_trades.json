{"tiers": [{"total_exp_required": 0, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:stick", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 2}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:arrow", "quantity": 64}], "trader_exp": 1, "max_uses": 16, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:flint", "quantity": 32}], "trader_exp": 1, "max_uses": 16, "reward_exp": true}]}]}, {"total_exp_required": 10, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:feather", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 2}], "trader_exp": 10, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:bow", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:string", "quantity": 32}], "trader_exp": 5, "max_uses": 16, "reward_exp": true}]}]}, {"total_exp_required": 70, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:string", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 3}], "trader_exp": 20, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 2, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:crossbow", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:arrow", "quantity": 16, "functions": [{"function": "set_data", "data": 6}]}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 150, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:tripwire_hook", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 4}], "trader_exp": 30, "max_uses": 12, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:arrow", "quantity": 16, "functions": [{"function": "set_data", "data": 25}]}], "trader_exp": 15, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 3, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:trident", "quantity": 1, "functions": [{"function": "enchant_with_levels", "levels": {"min": 20, "max": 30}}]}], "trader_exp": 15, "max_uses": 3, "reward_exp": true}]}]}]}