{"tiers": [{"trades": [{"wants": [{"item": "minecraft:paper", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 4}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}, {"item": "minecraft:book", "quantity": 1}], "gives": [{"item": "minecraft:enchanted_book", "functions": [{"function": "enchant_book_for_trading", "base_cost": 1, "base_random_cost": 1, "per_level_random_cost": 1, "per_level_cost": 1}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:book", "quantity": 1}], "gives": [{"item": "minecraft:emerald", "quantity": 5}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:bookshelf", "quantity": 16}]}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:nametag", "quantity": 5}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:book", "quantity": 10}]}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:enchanted_book", "functions": [{"function": "specific_enchants", "enchants": [{"id": "fortune", "level": 3}]}]}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:enchanted_book", "functions": [{"function": "specific_enchants", "enchants": [{"id": "silk_touch", "level": 1}]}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:enchanted_book", "functions": [{"function": "specific_enchants", "enchants": [{"id": "efficiency", "level": 5}]}]}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:enchanted_book", "functions": [{"function": "specific_enchants", "enchants": [{"id": "sharpness", "level": 5}]}]}]}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:enchanted_book", "functions": [{"function": "specific_enchants", "enchants": [{"id": "protection", "level": 4}]}]}]}, {"wants": [{"item": "minecraft:emerald", "quantity": 2}], "gives": [{"item": "minecraft:enchanted_book", "functions": [{"function": "specific_enchants", "enchants": [{"id": "mending", "level": 1}]}]}]}]}]}