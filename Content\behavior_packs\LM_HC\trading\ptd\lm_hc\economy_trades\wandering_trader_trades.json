{"tiers": [{"groups": [{"num_to_select": 5, "trades": [{"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:sea_pickle", "quantity": 5}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:slime_ball", "quantity": 5}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:glowstone", "quantity": 5}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:nautilus_shell", "quantity": 2}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:fern", "quantity": 5}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:reeds"}]}, {"max_uses": 4, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:pumpkin"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:kelp"}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:cactus"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:dandelion"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:poppy"}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:blue_orchid"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:allium"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:azure_bluet"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:red_tulip"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:orange_tulip"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:white_tulip"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:pink_tulip"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:oxeye_daisy"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:cornflower"}]}, {"max_uses": 7, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:lily_of_the_valley"}]}, {"max_uses": 7, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:open_eyeblossom"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:wheat_seeds"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:beetroot_seeds"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:pumpkin_seeds"}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:melon_seeds"}]}, {"max_uses": 8, "weight": 6, "wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"choice": [{"item": "minecraft:oak_sapling"}, {"item": "minecraft:spruce_sapling"}, {"item": "minecraft:birch_sapling"}, {"item": "minecraft:jungle_sapling"}, {"item": "minecraft:acacia_sapling"}, {"item": "minecraft:dark_oak_sapling"}, {"item": "minecraft:cherry_sapling"}, {"item": "minecraft:mangrove_propagule"}, {"item": "minecraft:pale_oak_sapling"}]}]}, {"max_uses": 12, "weight": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"choice": [{"item": "minecraft:red_dye", "quantity": 3}, {"item": "minecraft:white_dye", "quantity": 3}, {"item": "minecraft:blue_dye", "quantity": 3}, {"item": "minecraft:pink_dye", "quantity": 3}, {"item": "minecraft:black_dye", "quantity": 3}, {"item": "minecraft:green_dye", "quantity": 3}, {"item": "minecraft:light_gray_dye", "quantity": 3}, {"item": "minecraft:magenta_dye", "quantity": 3}, {"item": "minecraft:yellow_dye", "quantity": 3}, {"item": "minecraft:gray_dye", "quantity": 3}, {"item": "minecraft:purple_dye", "quantity": 3}, {"item": "minecraft:light_blue_dye", "quantity": 3}, {"item": "minecraft:lime_dye", "quantity": 3}, {"item": "minecraft:orange_dye", "quantity": 3}, {"item": "minecraft:brown_dye", "quantity": 3}, {"item": "minecraft:cyan_dye", "quantity": 3}]}]}, {"max_uses": 8, "weight": 5, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"choice": [{"item": "minecraft:tube_coral_block"}, {"item": "minecraft:brain_coral_block"}, {"item": "minecraft:bubble_coral_block"}, {"item": "minecraft:fire_coral_block"}, {"item": "minecraft:horn_coral_block"}]}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"choice": [{"item": "minecraft:vine"}, {"item": "minecraft:pale_hanging_moss"}]}]}, {"max_uses": 12, "weight": 2, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"choice": [{"item": "minecraft:brown_mushroom"}, {"item": "minecraft:red_mushroom"}]}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:waterlily", "quantity": 2}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:small_dripleaf_block", "quantity": 2}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:sand", "quantity": 8}]}, {"max_uses": 6, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:red_sand", "quantity": 4}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:pointed_dripstone", "quantity": 2}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:dirt_with_roots", "quantity": 2}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"choice": [{"item": "minecraft:moss_block", "quantity": 2}, {"item": "minecraft:pale_moss_block", "quantity": 2}]}]}]}, {"num_to_select": 1, "trades": [{"max_uses": 4, "wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:bucket:4"}]}, {"max_uses": 4, "wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:bucket:5"}]}, {"max_uses": 6, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:packed_ice"}]}, {"max_uses": 6, "wants": [{"item": "minecraft:emerald", "quantity": 6}], "gives": [{"item": "minecraft:blue_ice"}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:gunpowder"}]}, {"max_uses": 6, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:podzol", "quantity": 3}]}]}]}]}