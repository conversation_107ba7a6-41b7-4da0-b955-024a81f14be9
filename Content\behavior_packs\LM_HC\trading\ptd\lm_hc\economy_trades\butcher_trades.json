{"tiers": [{"total_exp_required": 0, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:chicken", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 5}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}, {"wants": [{"item": "minecraft:rabbit", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 6}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}, {"wants": [{"item": "minecraft:porkchop", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 5}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:cooked_chicken", "quantity": 16}], "trader_exp": 1, "max_uses": 16, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:cooked_porkchop", "quantity": 16}], "trader_exp": 1, "max_uses": 16, "reward_exp": true}]}]}, {"total_exp_required": 10, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:coal:0", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 3}], "trader_exp": 2, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:cooked_beef", "quantity": 16}], "trader_exp": 5, "max_uses": 16, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:cooked_mutton", "quantity": 16}], "trader_exp": 5, "max_uses": 16, "reward_exp": true}]}]}, {"total_exp_required": 70, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:beef", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 5}], "trader_exp": 20, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:rabbit_stew", "quantity": 10}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:golden_carrot", "quantity": 10}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 150, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:mutton", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 5}], "trader_exp": 20, "max_uses": 16, "reward_exp": true}]}, {"num_to_select": 2, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:golden_apple", "quantity": 8}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 5, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:enchanted_golden_apple", "quantity": 2}], "trader_exp": 15, "max_uses": 3, "reward_exp": true}]}]}]}