import { MinecraftDimensionTypes, Player, Vector3 } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { getRandomInt } from '../../utilities/rng';
import { onEatMeal } from '../events';
import { EntityQuantityConfig, spawnEntitiesWithInterval } from '../../utilities/summonEntity';

/**
 * Event 22: Shulker Hell
 * Spawns 10 shulkers around the player that immediately attack
 * @param player The player who triggered the event
 */
export function event22(player: Player): void {
  if (player.dimension.id !== MinecraftDimensionTypes.overworld) {
    onEatMeal(player);
    return;
  }
  try {
    // Configure shulker entity type and quantity
    const entityConfigs: EntityQuantityConfig[] = [{ entityId: 'minecraft:shulker', count: 10 }];

    /**
     * Finds valid spawn positions in a sphere around the player
     * @returns Valid spawn Vector3 position or undefined if none found
     */
    const getSpawnLocation = (): Vector3 | undefined => {
      // Set radius range for spawn area
      const minRadius: number = 4;
      const maxRadius: number = 8;

      // Get a random location near the player
      const spawnPos = getRandomLocation(
        player.location,
        player.dimension,
        minRadius,
        getRandomInt(0, maxRadius - minRadius),
        getRandomInt(-3, 4), // Y variation for more dynamic positioning
        true // Check for air blocks
      );

      if (!spawnPos) return undefined;

      // Play teleport particle effect at spawn location
      player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);

      return spawnPos;
    };

    // Start spawning shulkers with a 3 tick delay between each
    spawnEntitiesWithInterval(
      player.dimension,
      entityConfigs,
      getSpawnLocation,
      3 // Faster spawn rate than event10 for more intense experience
    ).catch((error) => {
      console.warn(`Error in event22 spawning process: ${error}`);
    });
  } catch (error) {
    console.warn(`Failed to execute event 22: ${error}`);
  }
  return;
}
