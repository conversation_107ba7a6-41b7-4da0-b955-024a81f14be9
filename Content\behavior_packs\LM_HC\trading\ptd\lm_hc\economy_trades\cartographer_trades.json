{"tiers": [{"total_exp_required": 0, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:paper", "quantity": 1, "price_multiplier": 0.01}], "gives": [{"item": "minecraft:emerald", "quantity": 4}], "trader_exp": 2, "max_uses": 64, "reward_exp": true}]}, {"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:empty_map", "quantity": 8}], "trader_exp": 1, "max_uses": 16, "reward_exp": true}]}]}, {"total_exp_required": 10, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:compass", "quantity": 1, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:emerald", "quantity": 5}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}]}, {"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}, {"item": "minecraft:compass", "quantity": 1}], "gives": [{"item": "minecraft:filled_map", "functions": [{"function": "exploration_map", "destination": "monument"}]}], "trader_exp": 5, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 70, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 1, "price_multiplier": 0.05}, {"item": "minecraft:compass", "quantity": 1}], "gives": [{"item": "minecraft:filled_map", "functions": [{"function": "exploration_map", "destination": "mansion"}]}], "trader_exp": 10, "max_uses": 12, "reward_exp": true}]}, {"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 2, "price_multiplier": 0.05}], "gives": [{"item": "minecraft:banner", "quantity": 4}], "trader_exp": 15, "max_uses": 12, "reward_exp": true}]}]}, {"total_exp_required": 150, "groups": [{"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 2, "price_multiplier": 0.05}, {"item": "minecraft:compass", "quantity": 1}], "gives": [{"item": "minecraft:filled_map", "functions": [{"function": "exploration_map", "destination": "buriedtreasure"}]}], "trader_exp": 20, "max_uses": 12, "reward_exp": true}]}, {"num_to_select": 1, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 3, "price_multiplier": 0.05}, {"item": "minecraft:compass", "quantity": 1}], "gives": [{"item": "minecraft:filled_map", "functions": [{"function": "exploration_map", "destination": "ruins"}]}], "trader_exp": 30, "max_uses": 12, "reward_exp": true}]}]}]}