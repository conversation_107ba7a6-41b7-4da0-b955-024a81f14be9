{"format_version": "1.8.0", "animations": {"animation.lucky_pot.idle": {"loop": true, "bones": {"lucky_pot": {"scale": [1, "1+math.sin((q.anim_time)*90)*0.05", 1]}}}, "animation.lucky_pot.give_food": {"loop": "hold_on_last_frame", "animation_length": 1.1667, "bones": {"lucky_pot": {"rotation": {"0.0": [0, 0, "math.sin((q.anim_time)*1440)*1"], "0.2083": [0, 0, "math.sin((q.anim_time)*2880)*1"], "0.9167": [0, 0, 0]}, "position": {"0.5833": [0, 0, 0], "0.6667": [0, "11+math.sin((q.anim_time)*180)*1", 0], "0.7917": [0, "12+math.sin((q.anim_time)*180)*1", 0], "0.9167": [0, "math.sin((q.anim_time)*180)*1", 0]}, "scale": {"0.0": [1, 1, 1], "0.5": [1, 0.6, 1], "0.5833": [1, 1.8, 1], "0.6667": [1.2, 1.5, 1.2], "0.7917": ["1.2+math.sin((q.anim_time)*180)*0.01", 1, "1.2+math.sin((q.anim_time)*180)*0.01"], "0.9167": [1, 1, 1], "0.9583": [1, 0.7, 1], "1.0": [1, 0.6, 1], "1.0833": [1, 0.6, 1], "1.1667": [1, 1, 1]}}, "bottom_pot": {"scale": {"0.5833": [1, 1, 1], "0.6667": [0.9, 1.3, 0.9], "0.8333": [1, 0.9, 1], "0.9167": [1, 1, 1]}}, "right_handle": {"rotation": {"0.0": [0, 0, 0], "0.5": [0, 0, 0], "0.6667": [0, 0, -7.5], "0.75": [0, 0, 152.5], "0.9167": [0, 0, 162.5], "1.0": [0, 0, -12.5], "1.0833": [0, 0, 17.5], "1.1667": [0, 0, 0]}}, "left_handle": {"rotation": {"0.0": [0, 0, 0], "0.5": [0, 0, 0], "0.6667": [0, 0, 7.5], "0.75": [0, 0, -152.5], "0.9167": [0, 0, -162.5], "1.0": [0, 0, 12.5], "1.0833": [0, 0, -17.5], "1.1667": [0, 0, 0]}}}, "particle_effects": {"0.0": [{"effect": "bloom_bubble_01", "locator": "locator9"}, {"effect": "charge_lines_01"}, {"effect": "charge_orb_01", "locator": "locator6"}, {"effect": "charge_orb_02", "locator": "locator9"}, {"effect": "potglow_01", "locator": "locator10"}], "0.25": {"effect": "bloom_stars_01", "locator": "locator9"}, "0.5417": [{"effect": "bloom_glow_01"}, {"effect": "bloom_glowshade_01", "locator": "locator"}, {"effect": "bloom_glowshade_02", "locator": "locator2"}, {"effect": "bloom_glowshade_03", "locator": "locator3"}, {"effect": "bloom_glowshade_04", "locator": "locator4"}, {"effect": "bloom_lines_01"}, {"effect": "bloom_stars_02"}, {"effect": "pop_01", "locator": "locator9"}], "0.875": [{"effect": "land_01"}, {"effect": "land_02"}, {"effect": "land_03"}]}}, "animation.lucky_pot.idle_particles": {"loop": true, "particle_effects": {"0.0": [{"effect": "glow_shade_01", "locator": "locator"}, {"effect": "glow_shade_02", "locator": "locator2"}, {"effect": "glow_shade_03", "locator": "locator3"}, {"effect": "glow_shade_04", "locator": "locator4"}, {"effect": "bubbles_01", "locator": "locator9"}, {"effect": "potglow_01", "locator": "locator10"}, {"effect": "stars_01", "locator": "locator9"}, {"effect": "stars_02", "locator": "locator9"}, {"effect": "potglow_02", "locator": "locator11"}]}}, "animation.lucky_pot.insert": {"animation_length": 0.25, "bones": {"lucky_pot": {"scale": {"0.0": [1, 1, 1], "0.0417": [1.1, 1, 1.1], "0.0833": [1.1, 1.1, 1.1], "0.125": [0.9, 1.1, 0.9], "0.2083": [0.9, 0.8, 0.9], "0.25": [1, 1, 1]}}}}, "animation.lucky_pot.swing": {"animation_length": 0.25, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, "-2.5 * v.swing_strength"], "0.25": [0, 0, 0]}}}}}}