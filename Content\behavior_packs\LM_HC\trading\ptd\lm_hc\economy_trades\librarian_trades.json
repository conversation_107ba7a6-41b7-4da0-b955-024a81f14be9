{
  "tiers": [
    {
      "total_exp_required": 0,
      "groups": [
        {
          "num_to_select": 1,
          "trades": [
            {
              "wants": [
                {
                  "item": "minecraft:paper",
                  "quantity": 1,
                  "price_multiplier": 0.01
                }
              ],
              "gives": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 2
                }
              ],
              "trader_exp": 2,
              "max_uses": 64,
              "reward_exp": true
            }
          ]
        },
        {
          "num_to_select": 2,
          "trades": [
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 2,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:bookshelf",
                  "quantity": 3
                }
              ],
              "trader_exp": 1,
              "max_uses": 12,
              "reward_exp": true
            },
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 1,
                  "price_multiplier": 0.05
                },
                {
                  "item": "minecraft:book",
                  "quantity": 1,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:book",
                  "quantity": 1,
                  "functions": [
                    {
                      "function": "enchant_book_for_trading",
                      "base_cost": 1,
                      "base_random_cost": 1,
                      "per_level_random_cost": 2,
                      "per_level_cost": 1
                    }
                  ]
                }
              ],
              "trader_exp": 1,
              "max_uses": 16,
              "reward_exp": true
            }
          ]
        }
      ]
    },
    {
      "total_exp_required": 10,
      "groups": [
        {
          "num_to_select": 1,
          "trades": [
            {
              "wants": [
                {
                  "item": "minecraft:book",
                  "quantity": 4,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 1
                }
              ],
              "trader_exp": 10,
              "max_uses": 12,
              "reward_exp": true
            }
          ]
        },
        {
          "num_to_select": 1,
          "trades": [
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 1,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:lantern",
                  "quantity": 1
                }
              ],
              "trader_exp": 5,
              "max_uses": 12,
              "reward_exp": true
            },
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "price_multiplier": 0.2
                },
                {
                  "item": "minecraft:book",
                  "quantity": 1,
                  "price_multiplier": 0.2
                }
              ],
              "gives": [
                {
                  "item": "minecraft:book",
                  "quantity": 1,
                  "functions": [
                    {
                      "function": "enchant_book_for_trading",
                      "base_cost": 2,
                      "base_random_cost": 5,
                      "per_level_random_cost": 10,
                      "per_level_cost": 3
                    }
                  ]
                }
              ],
              "trader_exp": 5,
              "max_uses": 12,
              "reward_exp": true
            }
          ]
        }
      ]
    },
    {
      "total_exp_required": 70,
      "groups": [
        {
          "num_to_select": 1,
          "trades": [
            {
              "wants": [
                {
                  "item": "minecraft:dye:0",
                  "quantity": 5,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 1
                }
              ],
              "trader_exp": 20,
              "max_uses": 12,
              "reward_exp": true
            }
          ]
        },
        {
          "num_to_select": 1,
          "trades": [
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 1,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:glass",
                  "quantity": 4
                }
              ],
              "trader_exp": 10,
              "max_uses": 12,
              "reward_exp": true
            },
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "price_multiplier": 0.2
                },
                {
                  "item": "minecraft:book",
                  "quantity": 1,
                  "price_multiplier": 0.2
                }
              ],
              "gives": [
                {
                  "item": "minecraft:book",
                  "quantity": 1,
                  "functions": [
                    {
                      "function": "enchant_book_for_trading",
                      "base_cost": 2,
                      "base_random_cost": 5,
                      "per_level_random_cost": 10,
                      "per_level_cost": 3
                    }
                  ]
                }
              ],
              "trader_exp": 10,
              "max_uses": 12,
              "reward_exp": true
            }
          ]
        }
      ]
    },

    {
      "total_exp_required": 150,
      "groups": [
        {
          "num_to_select": 1,
          "trades": [
            {
              // Book and quill can only have a single item per stack
              "wants": [
                {
                  "item": "minecraft:writable_book",
                  "quantity": 1,
                  "price_multiplier": 0.05
                },
                {
                  "item": "minecraft:writable_book",
                  "quantity": 1,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 1
                }
              ],
              "trader_exp": 30,
              "max_uses": 12,
              "reward_exp": true
            }
          ]
        },
        {
          "num_to_select": 1,
          "trades": [
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 4,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:compass",
                  "quantity": 1
                }
              ],
              "trader_exp": 15,
              "max_uses": 12,
              "reward_exp": true
            },
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 5,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:clock",
                  "quantity": 1
                }
              ],
              "trader_exp": 15,
              "max_uses": 12,
              "reward_exp": true
            },
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "price_multiplier": 0.2
                },
                {
                  "item": "minecraft:book",
                  "quantity": 1,
                  "price_multiplier": 0.2
                }
              ],
              "gives": [
                {
                  "item": "minecraft:book",
                  "quantity": 1,
                  "functions": [
                    {
                      "function": "enchant_book_for_trading",
                      "base_cost": 2,
                      "base_random_cost": 5,
                      "per_level_random_cost": 10,
                      "per_level_cost": 3
                    }
                  ]
                }
              ],
              "trader_exp": 15,
              "max_uses": 12,
              "reward_exp": true
            }
          ]
        },
        {
          "num_to_select": 1,
          "trades": [
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 5,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:name_tag",
                  "quantity": 1
                }
              ],
              "trader_exp": 30,
              "max_uses": 12,
              "reward_exp": true
            }
          ]
        }
      ]
    },
    {
      "total_exp_required": 250,
      "groups": [
        {
          "num_to_select": 1,
          "trades": [
            {
              "wants": [
                {
                  "item": "minecraft:emerald",
                  "quantity": 20,
                  "price_multiplier": 0.05
                }
              ],
              "gives": [
                {
                  "item": "minecraft:name_tag",
                  "quantity": 1
                }
              ],
              "trader_exp": 30,
              "max_uses": 12,
              "reward_exp": true
            }
          ]
        }
      ]
    }
  ]
}
