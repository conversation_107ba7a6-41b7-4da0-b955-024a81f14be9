import { getRandomInt } from './rng';
/**
 * Available horse variant events that can be triggered
 */
export const HORSE_VARIANT_EVENTS = [
    'minecraft:make_white',
    'minecraft:make_creamy',
    'minecraft:make_chestnut',
    'minecraft:make_brown',
    'minecraft:make_black',
    'minecraft:make_gray',
    'minecraft:make_darkbrown'
];
/**
 * Sets a random variant for a horse entity
 * @param entity - The horse entity to modify
 * @returns The selected variant event that was applied
 */
export function setRandomHorseVariant(entity) {
    // Select random variant event
    const variantIndex = getRandomInt(0, HORSE_VARIANT_EVENTS.length - 1);
    const variantEvent = HORSE_VARIANT_EVENTS[variantIndex];
    // Trigger the event to change the horse's variant
    entity.triggerEvent(variantEvent);
    return variantEvent;
}
